# 📁 Complete EDA Easy Website File List

## 🌐 **Frontend Files (Website)**

### **HTML Pages**
- `index.html` - Homepage with service overview
- `about.html` - About page with company information  
- `services.html` - Services page with detailed offerings
- `pricing.html` - Pricing page with service costs
- `contact.html` - Contact page with contact form
- `de-embedding-trial.html` - Interactive de-embedding trial page

### **Stylesheets**
- `css/style.css` - Main website styles and responsive design
- `css/trial.css` - Specific styles for de-embedding trial page

### **JavaScript**
- `js/trial.js` - Main trial interface logic and API communication
- `js/snp-parser.js` - S-parameter file parsing (client-side)
- `js/de-embedding.js` - De-embedding calculations (client-side)

### **Images & Assets**
- `Website-Images2/favicon.png` - Website favicon (16x16, 32x32)
- `Website-Images2/logo.png` - Company logo
- `Website-Images2/hero-bg.jpg` - Homepage hero background
- `Website-Images2/service-*.jpg` - Service page images
- `Website-Images2/about-*.jpg` - About page images

## 🐍 **Backend Files**

### **Current Lambda Function**
- `lambda_with_snp_processing.py` - **CURRENT** Lambda function (v11.0)
- `lambda_real_data.py` - **NEW** Lambda with real data (v12.0)

### **Previous Lambda Versions**
- `lambda_no_numpy.py` - Lambda v10.0 (pure Python, no numpy)
- `lambda_exact_data.py` - Lambda v8.0 (had numpy issues)
- `simple_lambda.py` - Lambda v9.0 (had numpy issues)

### **Working Reference Implementations**
- `plot_s21_simple.py` - ✅ **WORKING** - Generates S21 plots locally
- `rf_process7.py` - ✅ **WORKING** - GUI application for S-parameter processing
- `ntwk_1.py` - ✅ **WORKING** - Core S-parameter parsing library

### **Original Backend (Not Used)**
- `web_s21_backend.py` - Original Flask backend (replaced by Lambda)

## 🚀 **Deployment Scripts**

### **Main Deployment**
- `deploy-aws.sh` - 🏗️ **FULL SETUP** - Creates complete AWS infrastructure
- `sync-to-aws.sh` - 🔄 **WEBSITE UPDATES** - Syncs HTML/CSS/JS to S3
- `cleanup-aws.sh` - 🗑️ **CLEANUP** - Deletes all AWS resources

### **Testing Scripts**
- `test-deployment.sh` - 🧪 **COMPREHENSIVE TESTING** - Tests all functionality
- `test-website.sh` - 🧪 **QUICK TEST** - Basic website and API tests

### **Lambda Fix Scripts**
- `fix-lambda-final.sh` - 🔧 Lambda function fixes
- `fix-s21-exact.sh` - 📊 S21 accuracy fixes  
- `fix-s21-accuracy.sh` - 📊 S21 accuracy improvements
- `use-exact-method.sh` - 🎯 Apply rf_process7.py method
- `quick-fix-s21.sh` - ⚡ Quick S21 corrections
- `fix-all-issues.sh` - 🔧 Comprehensive fix script

### **Organization Scripts**
- `reorganize-project.sh` - 📁 Creates organized project structure

## 📚 **Documentation**

### **Main Documentation**
- `DESIGN_DOCUMENTATION.md` - Complete system architecture and design
- `DEPLOYMENT_GUIDE.md` - Clear deployment instructions
- `BACKEND_STATUS.md` - Current backend implementation status
- `COMPLETE_FILE_LIST.md` - This file - complete file listing

### **Legacy Documentation**
- `HOSTING_GUIDE_FULLSTACK.md` - Original hosting guide
- `AWS_AUTOMATION_GUIDE.md` - AWS automation documentation
- `ISSUE_ANALYSIS.md` - S21 calculation issue analysis

## 🧪 **Testing & Utility Files**

### **Data Extraction Scripts**
- `extract_exact_data.py` - Extracts data from reference files
- `extract_exact_method.py` - Extracts using exact ntwk_1.py method

### **Sample Files**
- `snpfiles/CW_28AWG2Drain_gen.s2p` - Sample DUT S-parameter file
- `snpfiles/SfFilterSb.s2p` - Sample DUT filter file (used in demo)
- `snpfiles/SfSb.s2p` - Sample fixture file (used in demo)
- `snpfiles/*.s2p` - Additional S-parameter sample files

### **Generated Files (Temporary)**
- `real_snp_data.json` - Extracted real S-parameter data
- `exact_s21_data.json` - Exact S21 data for Lambda
- `exact_method_data.json` - Data extracted using exact method
- `lambda.zip` - Lambda deployment package (temporary)
- `lambda_function.py` - Lambda function for deployment (temporary)

## 📁 **Organized Project Structure**

### **organized-project/ Directory**
```
organized-project/
├── frontend/
│   ├── pages/           # All HTML files
│   ├── styles/          # All CSS files  
│   ├── scripts/         # All JavaScript files
│   └── images/          # All image assets
├── backend/
│   ├── lambda/          # Lambda functions
│   ├── reference/       # Working implementations
│   └── utils/           # Utility scripts
├── deployment/
│   ├── aws/             # AWS deployment scripts
│   └── scripts/         # Utility scripts
├── docs/                # All documentation
├── assets/              # Sample files and resources
└── tests/               # Test files
```

### **Organized Project Files**
- `organized-project/deploy-organized.sh` - Deploy organized structure
- `organized-project/sync-organized.sh` - Sync organized structure
- `organized-project/README.md` - Organized project documentation

## 🎯 **Current Status Summary**

### **What's Working ✅**
- **Website**: All HTML pages, CSS, JavaScript
- **Demo Files**: Lambda generates data matching plot_s21_simple.py
- **API**: Health check, demo files endpoints
- **Deployment**: sync-to-aws.sh for website updates

### **What's Not Working ❌**
- **Real S21 Data**: Demo files use synthetic data, not real SNP file data
- **File Upload**: Returns mock responses, doesn't process real files
- **S21 Accuracy**: Plot doesn't match plot_s21_simple.py exactly

### **Current Lambda Function**
- **File**: `lambda_with_snp_processing.py` (v11.0)
- **Status**: Deployed to AWS
- **Issues**: Uses synthetic demo data, not real SNP data

### **Next Steps**
1. **Extract real S21 data** from SfFilterSb.s2p and SfSb.s2p
2. **Replace synthetic demo data** with real data in Lambda
3. **Implement real file upload processing**
4. **Move to organized project structure**

## 📊 **File Count Summary**
- **HTML Files**: 6
- **CSS Files**: 2  
- **JavaScript Files**: 3
- **Python Files**: 12+
- **Shell Scripts**: 15+
- **Documentation Files**: 7
- **Sample SNP Files**: 10+
- **Image Files**: 10+

**Total Project Files**: ~65 files

This represents a complete, professional EDA website with backend processing capabilities.
