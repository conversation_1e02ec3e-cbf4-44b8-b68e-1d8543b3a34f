# 🚀 EDA Easy Deployment Guide

## 📋 **SIMPLE RULE: When to Use Which Script**

### **🎯 MOST COMMON: Website Content Changes**
```bash
# Changed HTML, CSS, JavaScript, or images?
./sync-to-aws.sh
```

### **🏗️ FIRST TIME SETUP**
```bash
# Starting from scratch?
./deploy-aws.sh
```

### **🔧 BACKEND CHANGES (Lambda Function)**
```bash
# Changed Python backend logic?
# Use AWS CLI directly (fastest):
cp new_lambda.py lambda_function.py
zip lambda.zip lambda_function.py
aws lambda update-function-code --function-name eda-easy-backend --zip-file fileb://lambda.zip
```

### **🧪 TESTING**
```bash
# Want to test if everything works?
./test-deployment.sh
```

### **🗑️ NUCLEAR OPTION**
```bash
# Something is broken, start over?
./cleanup-aws.sh
./deploy-aws.sh
```

## 🎯 **Common Scenarios**

### **Scenario 1: First Time Setup**
```bash
# Start from scratch
./deploy-aws.sh
./test-deployment.sh
```

### **Scenario 2: Update Website Content**
```bash
# Changed HTML, CSS, or JS files
./sync-to-aws.sh
```

### **Scenario 3: Update Lambda Function**
```bash
# Changed backend logic
# Use AWS CLI directly (as we did for numpy fix):
cp new_lambda.py lambda_function.py
zip lambda.zip lambda_function.py
aws lambda update-function-code --function-name eda-easy-backend --zip-file fileb://lambda.zip --region us-west-2
```

### **Scenario 4: Fix S21 Issues**
```bash
# If S21 plots are wrong
./fix-s21-exact.sh
# OR
./use-exact-method.sh
```

### **Scenario 5: Complete Cleanup**
```bash
# Remove everything and start over
./cleanup-aws.sh
./deploy-aws.sh
```

---

## 📁 **File Organization Guide**

### **Current Project Structure (Messy)**
```
eda-easy-website/
├── *.html                     # Website pages
├── css/                       # Stylesheets
├── js/                        # JavaScript files
├── Website-Images2/           # Images
├── snpfiles/                  # Sample S-parameter files
├── *.py                       # Backend and test files (MIXED)
├── *.sh                       # Deployment scripts (MIXED)
├── *.md                       # Documentation (MIXED)
└── organized-project/         # Clean organized version
```

### **Organized Project Structure (Clean)**
```
organized-project/
├── frontend/                  # All website files
│   ├── pages/                # HTML files
│   ├── styles/               # CSS files
│   ├── scripts/              # JavaScript files
│   └── images/               # Image assets
├── backend/                   # All backend files
│   ├── lambda/               # Lambda functions
│   ├── reference/            # Working implementations
│   └── utils/                # Utility scripts
├── deployment/                # All deployment scripts
│   ├── aws/                  # AWS-specific scripts
│   └── scripts/              # Utility scripts
├── docs/                      # All documentation
└── assets/                    # Sample files and resources
```

---

## 🔧 **Detailed Script Explanations**

### **1. deploy-aws.sh** 🏗️
**Purpose**: Complete AWS infrastructure setup
**Input**: None (uses current directory files)
**Output**:
- S3 bucket for website hosting
- Lambda function for backend
- API Gateway for REST API
- Proper permissions and CORS

**When to use**:
- ✅ First time setup
- ✅ After running cleanup-aws.sh
- ❌ For small updates (use sync-to-aws.sh instead)

**Expected result**: Working website with backend API

### **2. sync-to-aws.sh** 🔄
**Purpose**: Update website files only (HTML, CSS, JS, images)
**Input**: Current directory website files
**Output**: Updated S3 bucket content

**When to use**:
- ✅ Changed HTML content
- ✅ Updated CSS styles
- ✅ Modified JavaScript
- ✅ Added/changed images
- ❌ Lambda function changes (use AWS CLI directly)

**Expected result**: Website reflects your local changes

### **3. test-deployment.sh** 🧪
**Purpose**: Comprehensive testing of all functionality
**Input**: None (tests live website)
**Output**: Test results for:
- Website accessibility
- API health check
- Demo files functionality
- File upload endpoints

**When to use**:
- ✅ After any deployment
- ✅ To verify everything works
- ✅ Before showing to clients

**Expected result**: All tests pass ✅

### **4. cleanup-aws.sh** 🗑️
**Purpose**: Delete ALL AWS resources (DESTRUCTIVE!)
**Input**: None
**Output**: Deleted S3 bucket, Lambda function, API Gateway

**When to use**:
- ✅ Starting over completely
- ✅ Removing test deployments
- ❌ For small fixes (too destructive)

**Expected result**: No AWS resources, no charges

### **5. Lambda Update Scripts** 🔧

#### **fix-lambda-final.sh**
- **Purpose**: Update Lambda with exact S21 data
- **When**: S21 calculations are wrong
- **Method**: Deploys pre-built Lambda function

#### **fix-s21-exact.sh**
- **Purpose**: Extract exact data from your files
- **When**: Need to match plot_s21_simple.py exactly
- **Method**: Uses ntwk_1.py to extract real data

#### **use-exact-method.sh**
- **Purpose**: Apply rf_process7.py method to Lambda
- **When**: Want exact same processing as local
- **Method**: Copies rf_process7.py approach

---

## 🧪 **Testing Scripts**

### **test-website.sh**
**Purpose**: Quick website functionality test
**Tests**:
- API health check
- Demo files endpoint
- Website accessibility

**Usage**:
```bash
./test-website.sh
```

**Expected output**:
```
✅ API Health Check
✅ Demo Files Endpoint
✅ Website Accessibility
```

---

## 📊 **Current Status (What We Actually Used)**

### **What We Used for Current Deployment**:
1. ✅ `deploy-aws.sh` - Initial AWS setup
2. ✅ `sync-to-aws.sh` - Website file updates
3. ✅ **Direct AWS CLI** - Lambda function updates (bypassed scripts)

### **Why We Bypassed Scripts**:
- **Numpy issue**: Needed quick Lambda fix
- **Iteration speed**: Direct AWS CLI was faster
- **Debugging**: Easier to see exact commands

### **Scripts We Didn't Use**:
- ❌ `fix-lambda-final.sh` - Created but not used
- ❌ `fix-s21-exact.sh` - Created but not used
- ❌ `use-exact-method.sh` - Created but not used

---

## 🎯 **Recommended Workflow**

### **For Development**:
```bash
# 1. Make changes locally
nano index.html

# 2. Test locally (optional)
cd frontend/pages && python3 -m http.server 8000

# 3. Deploy website changes
./sync-to-aws.sh

# 4. Deploy Lambda changes (if needed)
cp new_lambda.py lambda_function.py
zip lambda.zip lambda_function.py
aws lambda update-function-code --function-name eda-easy-backend --zip-file fileb://lambda.zip

# 5. Test everything
./test-deployment.sh
```

### **For Production**:
```bash
# 1. Use organized structure
cd organized-project

# 2. Deploy organized version
./deploy-organized.sh

# 3. Test thoroughly
./test-deployment.sh
```

---

## 🔄 **Next Steps: Better Organization**

1. **Move to organized-project/** structure
2. **Consolidate deployment scripts**
3. **Create single update script** for both website and Lambda
4. **Add real SNP file processing** to Lambda
5. **Implement proper testing suite**

This guide should make the deployment process much clearer!
