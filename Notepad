✅ Lambda function created: eda-easy-backend
🌐 Creating API Gateway...
✅ API Gateway created: fh73i40lo2
{
    "httpMethod": "ANY",
    "authorizationType": "NONE",
    "apiKeyRequired": false
}
{
    "type": "AWS_PROXY",
    "httpMethod": "POST",
    "uri": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:936654323206:function:eda-easy-backend/invocations",
    "passthroughBehavior": "WHEN_NO_MATCH",
    "timeoutInMillis": 29000,
    "cacheNamespace": "iiij30",
    "cacheKeyParameters": []
}
{
    "Statement": "{\"Sid\":\"api-gateway-invoke\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"apigateway.amazonaws.com\"},\"Action\":\"lambda:InvokeFunction\",\"Resource\":\"arn:aws:lambda:us-west-2:936654323206:function:eda-easy-backend\",\"Condition\":{\"ArnLike\":{\"AWS:SourceArn\":\"arn:aws:execute-api:us-west-2:936654323206:fh73i40lo2/*/*\"}}}"
}
{
    "id": "55i2kt",
    "createdDate": "2025-05-29T00:02:33-07:00"
}
✅ API Gateway deployed: https://fh73i40lo2.execute-api.us-west-2.amazonaws.com/prod
🔧 Updating frontend configuration...
upload: js/trial.js to s3://eda-easy-website-1748501958/js/trial.js
✅ Frontend updated with API URL
🎉 Deployment completed successfully!

📋 Deployment Summary:
Website URL: http://eda-easy-website-1748501958.s3-website-us-west-2.amazonaws.com
API URL: https://fh73i40lo2.execute-api.us-west-2.amazonaws.com/prod
S3 Bucket: eda-easy-website-1748501958
Lambda Function: eda-easy-backend
API Gateway: fh73i40lo2

⏳ Note: It may take a few minutes for all services to be fully available.

2025/05/29 9:46am ----------------------------------------
(dalle_env) brittany@brittany-linux:~/Documents/website$ cd /home/<USER>/Documents/website && ./sync-to-aws.sh
🔄 Syncing local changes to AWS S3...
Bucket: eda-easy-website-1748501958
📋 Checking for changes...
upload: ./Notepad to s3://eda-easy-website-1748501958/Notepad                     
upload: ./de-embedding-trial.html to s3://eda-easy-website-1748501958/de-embedding-trial.html
upload: js/trial.js to s3://eda-easy-website-1748501958/js/trial.js
✅ Website files synced to S3

🎉 Sync completed!
Your website: http://eda-easy-website-1748501958.s3-website-us-west-2.amazonaws.com

💡 Pro tip: Bookmark this command for quick updates:
./sync-to-aws.sh