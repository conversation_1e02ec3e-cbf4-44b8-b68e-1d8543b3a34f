# 🌐 EDA Easy Website

Professional EDA (Electronic Design Automation) website with S21 parameter processing capabilities.

## 🚀 **Quick Start**

### **Local Development**
```bash
# 1. Setup environment
./scripts/setup_snp_server.sh

# 2. Start development server
./scripts/start_web_backend.sh

# 3. Open browser
open http://localhost:8000
```

### **Deploy to AWS**
```bash
# 1. Configure AWS CLI
aws configure

# 2. Deploy to AWS
./scripts/deploy-aws.sh

# 3. Test deployment
./scripts/test-deployment.sh
```

---

## 📁 **Project Structure**

```
website_joshua/
├── 📄 Frontend
│   ├── *.html              # Website pages
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript
│   └── img/               # Images
│
├── 🐍 Backend
│   ├── lambda_no_numpy.py # ✅ Working AWS Lambda
│   ├── rf_process7.py     # ✅ Local S21 processor
│   └── *.py               # Other Python files
│
├── 🚀 scripts/
│   ├── deploy-aws.sh      # AWS deployment
│   ├── sync-to-aws.sh     # Update deployment
│   ├── setup_*.sh         # Environment setup
│   └── README.md          # Script documentation
│
├── 📚 docs/
│   ├── LOCAL_DEVELOPMENT_GUIDE.md  # Local setup
│   ├── AWS_DEPLOYMENT_GUIDE.md     # AWS deployment
│   ├── PROJECT_STRUCTURE_GUIDE.md  # Project overview
│   └── *.md                        # Other documentation
│
└── 📦 Dependencies
    ├── requirements.txt         # Python packages
    └── snp_requirements.txt     # S-parameter packages
```

---

## 🎯 **Key Features**

### **Website Capabilities**
- ✅ **Responsive Design** - Works on desktop and mobile
- ✅ **Professional Layout** - Clean, modern EDA business design
- ✅ **Fast Loading** - Optimized images and code
- ✅ **SEO Optimized** - Proper meta tags and structure

### **S21 Processing**
- ✅ **Local Processing** - Process S-parameter files locally
- ✅ **AWS Lambda** - Cloud-based S21 processing
- ✅ **Multiple Formats** - Support for .s2p files
- ✅ **Real-time Plotting** - Interactive S21 magnitude plots

### **Deployment Options**
- ✅ **AWS S3** - Static website hosting
- ✅ **AWS Lambda** - Serverless backend processing
- ✅ **API Gateway** - RESTful API endpoints
- ✅ **Local Development** - Full local testing environment

---

## 🔧 **Development Workflows**

### **Frontend Development**
```bash
# Start simple HTTP server
python3 -m http.server 8000

# Edit HTML/CSS/JS files
# Refresh browser to see changes
```

### **Backend Development**
```bash
# Start backend with S21 processing
./scripts/start_web_backend.sh

# Test S21 functionality
open http://localhost:5002/simple_s21_test.html

# Edit Python files and restart server
```

### **Full Stack Testing**
```bash
# Test website functionality
./scripts/test-website.sh

# Test S21 processing
python3 snp_processor.py snpfiles/SfFilterSb.s2p

# Test complete deployment
./scripts/test-deployment.sh
```

---

## 📚 **Documentation**

| Guide | Purpose |
|-------|---------|
| [Local Development](docs/LOCAL_DEVELOPMENT_GUIDE.md) | Complete local setup guide |
| [AWS Deployment](docs/AWS_DEPLOYMENT_GUIDE.md) | AWS deployment instructions |
| [Project Structure](docs/PROJECT_STRUCTURE_GUIDE.md) | Understanding the codebase |
| [Scripts Usage](scripts/README.md) | All automation scripts |
| [S21 Solution](docs/S21_Plotting_Solution.md) | S-parameter implementation |

---

## 🚀 **Deployment Commands**

### **Most Common Commands**
```bash
# Update website content
./scripts/sync-to-aws.sh

# Full deployment (first time)
./scripts/deploy-aws.sh

# Test everything works
./scripts/test-deployment.sh

# Clean slate (if broken)
./scripts/cleanup-aws.sh && ./scripts/deploy-aws.sh
```

### **Backward Compatibility**
Old script paths still work:
```bash
./deploy-aws.sh      # → redirects to scripts/deploy-aws.sh
./sync-to-aws.sh     # → redirects to scripts/sync-to-aws.sh
./cleanup-aws.sh     # → redirects to scripts/cleanup-aws.sh
```

---

## 🧪 **Testing**

### **Local Testing**
```bash
# Test frontend
python3 -m http.server 8000

# Test backend
./scripts/start_web_backend.sh

# Test S21 processing
python3 snp_processor.py snpfiles/SfFilterSb.s2p
```

### **AWS Testing**
```bash
# Deploy and test
./scripts/deploy-aws.sh
./scripts/test-deployment.sh

# Test specific functionality
curl https://your-api-url.com/health
```

---

## 🔍 **Troubleshooting**

### **Common Issues**
- **Port in use**: `lsof -i :8000` then `kill -9 <PID>`
- **Dependencies missing**: `pip3 install -r requirements.txt`
- **AWS CLI not configured**: `aws configure`
- **Scripts not executable**: `chmod +x scripts/*.sh`

### **Getting Help**
- Check `docs/` folder for specific guides
- Look at `scripts/README.md` for script usage
- Review `docs/ISSUE_ANALYSIS.md` for common problems

---

## 💡 **Pro Tips**

### **Development**
- Use VS Code with Live Server for auto-refresh
- Keep browser dev tools open for debugging
- Test locally before deploying to AWS
- Use `scripts/` for all automation

### **Deployment**
- Use `sync-to-aws.sh` for content updates (faster)
- Use `deploy-aws.sh` only for fresh deployments
- Always test with `test-deployment.sh` after changes
- Keep AWS costs low with proper resource cleanup

---

## 📞 **Support**

- **Documentation**: Check `docs/` directory
- **Scripts**: See `scripts/README.md`
- **Issues**: Review `docs/ISSUE_ANALYSIS.md`
- **S21 Problems**: Check `docs/S21_Plotting_Solution.md`

---

## 🎉 **Recent Updates**

### **Project Reorganization (Latest)**
- ✅ **Organized scripts** - All shell scripts moved to `scripts/` directory
- ✅ **Enhanced documentation** - Comprehensive guides for local and AWS development
- ✅ **Backward compatibility** - Old script paths still work via wrappers
- ✅ **Better structure** - Clear separation of frontend, backend, scripts, and docs

### **Working Features**
- ✅ **S21 Processing** - Both local and AWS Lambda implementations working
- ✅ **AWS Deployment** - Fully automated deployment to S3 + Lambda + API Gateway
- ✅ **Local Development** - Complete local testing environment
- ✅ **Documentation** - Comprehensive guides for all workflows

---

**🚀 Ready to get started? Check out the [Local Development Guide](docs/LOCAL_DEVELOPMENT_GUIDE.md)!**

---
### Ready to Use:
Local development:  docs/LOCAL_DEVELOPMENT_GUIDE.md
AWS deployment:  docs/AWS_DEPLOYMENT_GUIDE.md
Script usage:  scripts/README.md
Project overview:  README.md
