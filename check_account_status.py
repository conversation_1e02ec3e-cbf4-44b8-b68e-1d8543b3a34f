#!/usr/bin/env python3
"""
Check OpenAI account status and billing
"""

import os
import requests
import json

def check_account_details():
    """Check account information"""
    print("🔍 Checking Account Details...")
    print("-" * 35)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ No API key found")
        return False
    
    headers = {'Authorization': f'Bearer {api_key}'}
    
    # Check organization (if any)
    try:
        print("📋 Checking organization...")
        response = requests.get('https://api.openai.com/v1/organizations', headers=headers, timeout=10)
        
        if response.status_code == 200:
            orgs = response.json()
            print(f"✅ Organizations: {len(orgs.get('data', []))}")
            for org in orgs.get('data', []):
                print(f"   - {org.get('name', 'Unknown')} ({org.get('id', 'No ID')})")
        else:
            print(f"⚠️  Organization check: {response.status_code}")
            
    except Exception as e:
        print(f"⚠️  Organization check error: {str(e)}")
    
    # Check if we can access billing
    try:
        print("\n💳 Checking billing access...")
        response = requests.get('https://api.openai.com/v1/dashboard/billing/subscription', headers=headers, timeout=10)
        
        if response.status_code == 200:
            billing = response.json()
            print("✅ Billing access successful")
            print(f"📊 Subscription: {json.dumps(billing, indent=2)}")
        else:
            print(f"❌ Billing access failed: {response.status_code}")
            print(f"📝 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Billing check error: {str(e)}")

def test_minimal_request():
    """Test with absolute minimal request"""
    print("\n🧪 Testing Minimal Request...")
    print("-" * 32)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ No API key found")
        return False
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # Absolute minimal payload
    payload = {
        "model": "dall-e-3",
        "prompt": "cat",
        "n": 1,
        "size": "1024x1024"
    }
    
    print(f"📝 Minimal prompt: 'cat'")
    print("📤 Making minimal request...")
    
    try:
        response = requests.post(
            'https://api.openai.com/v1/images/generations',
            headers=headers,
            json=payload,
            timeout=60
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Minimal generation successful!")
            return True
        else:
            print(f"❌ Minimal generation failed")
            try:
                error_data = response.json()
                print(f"📝 Full error response:")
                print(json.dumps(error_data, indent=2))
            except:
                print(f"📝 Raw response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_dalle2():
    """Test with DALL-E 2 (might have different restrictions)"""
    print("\n🎨 Testing DALL-E 2...")
    print("-" * 25)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ No API key found")
        return False
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # DALL-E 2 payload
    payload = {
        "model": "dall-e-2",
        "prompt": "simple circuit",
        "n": 1,
        "size": "1024x1024"
    }
    
    print(f"📝 DALL-E 2 prompt: 'simple circuit'")
    print("📤 Making DALL-E 2 request...")
    
    try:
        response = requests.post(
            'https://api.openai.com/v1/images/generations',
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ DALL-E 2 generation successful!")
            result = response.json()
            image_url = result['data'][0]['url']
            print(f"🖼️  Image URL: {image_url[:50]}...")
            return True
        else:
            print(f"❌ DALL-E 2 generation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"📝 Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"📝 Raw error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Run account diagnostics"""
    print("🏥 OpenAI Account Diagnostics")
    print("=" * 40)
    
    # Check account details
    check_account_details()
    
    # Test minimal request
    minimal_success = test_minimal_request()
    
    # Test DALL-E 2
    dalle2_success = test_dalle2()
    
    # Summary and recommendations
    print("\n" + "=" * 40)
    print("🏁 DIAGNOSTIC SUMMARY")
    print("=" * 40)
    
    if not minimal_success and not dalle2_success:
        print("❌ Both DALL-E 2 and DALL-E 3 failed")
        print("\n💡 LIKELY ISSUES:")
        print("1. 💳 No credits/billing not set up")
        print("2. 🚫 Account doesn't have image generation enabled")
        print("3. 🔒 Account restrictions")
        
        print("\n🔧 SOLUTIONS:")
        print("1. Go to: https://platform.openai.com/account/billing")
        print("2. Add a payment method and credits")
        print("3. Check account limits and usage")
        print("4. Contact OpenAI support if billing is set up")
        
    elif dalle2_success and not minimal_success:
        print("✅ DALL-E 2 works, DALL-E 3 doesn't")
        print("💡 Use DALL-E 2 for now, or check DALL-E 3 specific limits")
        
    elif minimal_success:
        print("✅ Image generation is working!")
        print("💡 The issue might be with specific prompts or request format")
        
    print(f"\n📞 Need help? Check:")
    print("   - https://platform.openai.com/account/billing")
    print("   - https://platform.openai.com/account/usage")
    print("   - https://help.openai.com/")

if __name__ == "__main__":
    main()
