#!/bin/bash

# 🧹 Clean up partial deployment and redeploy correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🧹 Cleaning up partial deployment and redeploying...${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Get current region
REGION=$(aws configure get region || echo "us-west-2")
echo -e "${BLUE}Using region: $REGION${NC}"

# Clean up existing resources
echo -e "${BLUE}🧹 Cleaning up existing resources...${NC}"

# Delete S3 buckets
buckets=$(aws s3 ls | grep "eda-easy-website" | awk '{print $3}' || true)
for bucket in $buckets; do
    echo -e "${BLUE}Deleting S3 bucket: $bucket${NC}"
    aws s3 rm s3://$bucket --recursive || true
    aws s3 rb s3://$bucket --force || true
    print_status "Deleted S3 bucket: $bucket"
done

# Delete API Gateway
apis=$(aws apigateway get-rest-apis --query "items[?contains(name, 'eda-easy')].id" --output text || true)
for api_id in $apis; do
    echo -e "${BLUE}Deleting API Gateway: $api_id${NC}"
    aws apigateway delete-rest-api --rest-api-id $api_id || true
    print_status "Deleted API Gateway: $api_id"
done

# Delete Lambda functions (check both regions)
for region in us-east-1 us-west-2; do
    functions=$(aws lambda list-functions --region $region --query "Functions[?contains(FunctionName, 'eda-easy')].FunctionName" --output text || true)
    for function in $functions; do
        echo -e "${BLUE}Deleting Lambda function: $function in $region${NC}"
        aws lambda delete-function --function-name $function --region $region || true
        print_status "Deleted Lambda function: $function"
    done
done

# Delete IAM role
if aws iam get-role --role-name lambda-execution-role >/dev/null 2>&1; then
    echo -e "${BLUE}Deleting IAM role: lambda-execution-role${NC}"
    aws iam detach-role-policy --role-name lambda-execution-role --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole || true
    aws iam delete-role --role-name lambda-execution-role || true
    print_status "Deleted IAM role: lambda-execution-role"
fi

# Clean up local files
rm -f bucket-policy.json trust-policy.json lambda-deployment.zip
rm -rf lambda-deployment

print_status "Cleanup completed"

echo ""
echo -e "${BLUE}🚀 Starting fresh deployment...${NC}"

# Now run the fixed deployment script
./deploy-aws.sh

echo ""
echo -e "${GREEN}🎉 Fresh deployment completed!${NC}"
