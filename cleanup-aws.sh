#!/bin/bash

# 🧹 EDA Easy Website - AWS Cleanup Script
# This script removes all AWS resources created by the deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
PROJECT_NAME="eda-easy"
REGION="us-east-1"

echo -e "${RED}🧹 EDA Easy AWS Cleanup Script${NC}"
echo -e "${YELLOW}⚠️  WARNING: This will permanently delete ALL AWS resources!${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Confirmation prompt
confirm_cleanup() {
    echo -e "${BLUE}This will delete:${NC}"
    echo "• All S3 buckets with '$PROJECT_NAME' in the name"
    echo "• All Lambda functions with '$PROJECT_NAME' in the name"
    echo "• All API Gateways with '$PROJECT_NAME' in the name"
    echo "• IAM roles created for the project"
    echo ""
    
    read -p "Are you absolutely sure? Type 'DELETE' to confirm: " confirmation
    
    if [ "$confirmation" != "DELETE" ]; then
        echo -e "${BLUE}Cleanup cancelled.${NC}"
        exit 0
    fi
    
    echo -e "${RED}Starting cleanup in 5 seconds... Press Ctrl+C to cancel${NC}"
    sleep 5
}

# List resources before deletion
list_resources() {
    echo -e "${BLUE}📋 Listing resources to be deleted...${NC}"
    
    # List S3 buckets
    echo -e "${BLUE}S3 Buckets:${NC}"
    aws s3 ls | grep "$PROJECT_NAME" || echo "No matching S3 buckets found"
    
    # List Lambda functions
    echo -e "${BLUE}Lambda Functions:${NC}"
    aws lambda list-functions --query "Functions[?contains(FunctionName, '$PROJECT_NAME')].FunctionName" --output table || echo "No matching Lambda functions found"
    
    # List API Gateways
    echo -e "${BLUE}API Gateways:${NC}"
    aws apigateway get-rest-apis --query "items[?contains(name, '$PROJECT_NAME')].{Name:name,ID:id}" --output table || echo "No matching API Gateways found"
    
    echo ""
}

# Delete S3 buckets
cleanup_s3() {
    echo -e "${BLUE}🪣 Cleaning up S3 buckets...${NC}"
    
    # Get all buckets with project name
    buckets=$(aws s3 ls | grep "$PROJECT_NAME" | awk '{print $3}' || true)
    
    if [ -z "$buckets" ]; then
        print_warning "No S3 buckets found with '$PROJECT_NAME' in name"
        return
    fi
    
    for bucket in $buckets; do
        echo -e "${BLUE}Deleting bucket: $bucket${NC}"
        
        # Delete all objects in bucket (including versions)
        aws s3 rm s3://$bucket --recursive || true
        
        # Delete all object versions
        aws s3api delete-objects --bucket $bucket --delete "$(aws s3api list-object-versions --bucket $bucket --query '{Objects: Versions[].{Key:Key,VersionId:VersionId}}')" 2>/dev/null || true
        
        # Delete all delete markers
        aws s3api delete-objects --bucket $bucket --delete "$(aws s3api list-object-versions --bucket $bucket --query '{Objects: DeleteMarkers[].{Key:Key,VersionId:VersionId}}')" 2>/dev/null || true
        
        # Delete bucket
        aws s3 rb s3://$bucket --force || print_error "Failed to delete bucket: $bucket"
        
        print_status "Deleted S3 bucket: $bucket"
    done
}

# Delete Lambda functions
cleanup_lambda() {
    echo -e "${BLUE}⚡ Cleaning up Lambda functions...${NC}"
    
    # Get all Lambda functions with project name
    functions=$(aws lambda list-functions --query "Functions[?contains(FunctionName, '$PROJECT_NAME')].FunctionName" --output text || true)
    
    if [ -z "$functions" ]; then
        print_warning "No Lambda functions found with '$PROJECT_NAME' in name"
        return
    fi
    
    for function in $functions; do
        echo -e "${BLUE}Deleting Lambda function: $function${NC}"
        
        aws lambda delete-function --function-name $function || print_error "Failed to delete Lambda function: $function"
        
        print_status "Deleted Lambda function: $function"
    done
}

# Delete API Gateways
cleanup_api_gateway() {
    echo -e "${BLUE}🌐 Cleaning up API Gateways...${NC}"
    
    # Get all API Gateways with project name
    apis=$(aws apigateway get-rest-apis --query "items[?contains(name, '$PROJECT_NAME')].id" --output text || true)
    
    if [ -z "$apis" ]; then
        print_warning "No API Gateways found with '$PROJECT_NAME' in name"
        return
    fi
    
    for api_id in $apis; do
        echo -e "${BLUE}Deleting API Gateway: $api_id${NC}"
        
        aws apigateway delete-rest-api --rest-api-id $api_id || print_error "Failed to delete API Gateway: $api_id"
        
        print_status "Deleted API Gateway: $api_id"
    done
}

# Delete IAM roles
cleanup_iam() {
    echo -e "${BLUE}🔐 Cleaning up IAM roles...${NC}"
    
    # Delete lambda execution role
    role_name="lambda-execution-role"
    
    if aws iam get-role --role-name $role_name >/dev/null 2>&1; then
        echo -e "${BLUE}Deleting IAM role: $role_name${NC}"
        
        # Detach policies
        aws iam detach-role-policy --role-name $role_name --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole || true
        
        # Delete role
        aws iam delete-role --role-name $role_name || print_error "Failed to delete IAM role: $role_name"
        
        print_status "Deleted IAM role: $role_name"
    else
        print_warning "IAM role '$role_name' not found"
    fi
}

# Delete CloudWatch logs
cleanup_cloudwatch() {
    echo -e "${BLUE}📊 Cleaning up CloudWatch logs...${NC}"
    
    # Get all log groups with project name
    log_groups=$(aws logs describe-log-groups --query "logGroups[?contains(logGroupName, '$PROJECT_NAME')].logGroupName" --output text || true)
    
    if [ -z "$log_groups" ]; then
        print_warning "No CloudWatch log groups found with '$PROJECT_NAME' in name"
        return
    fi
    
    for log_group in $log_groups; do
        echo -e "${BLUE}Deleting log group: $log_group${NC}"
        
        aws logs delete-log-group --log-group-name $log_group || print_error "Failed to delete log group: $log_group"
        
        print_status "Deleted log group: $log_group"
    done
}

# Clean up local files
cleanup_local() {
    echo -e "${BLUE}🗂️  Cleaning up local files...${NC}"
    
    files_to_remove=(
        "lambda-deployment.zip"
        "bucket-policy.json"
        "trust-policy.json"
        "load_test_results.txt"
        "js/trial.js.bak"
    )
    
    for file in "${files_to_remove[@]}"; do
        if [ -f "$file" ]; then
            rm -f "$file"
            print_status "Removed local file: $file"
        fi
    done
    
    # Remove lambda-deployment directory
    if [ -d "lambda-deployment" ]; then
        rm -rf lambda-deployment
        print_status "Removed lambda-deployment directory"
    fi
}

# Verify cleanup
verify_cleanup() {
    echo -e "${BLUE}🔍 Verifying cleanup...${NC}"
    
    # Check S3 buckets
    remaining_buckets=$(aws s3 ls | grep "$PROJECT_NAME" || true)
    if [ -n "$remaining_buckets" ]; then
        print_warning "Some S3 buckets may still exist:"
        echo "$remaining_buckets"
    fi
    
    # Check Lambda functions
    remaining_functions=$(aws lambda list-functions --query "Functions[?contains(FunctionName, '$PROJECT_NAME')].FunctionName" --output text || true)
    if [ -n "$remaining_functions" ]; then
        print_warning "Some Lambda functions may still exist:"
        echo "$remaining_functions"
    fi
    
    # Check API Gateways
    remaining_apis=$(aws apigateway get-rest-apis --query "items[?contains(name, '$PROJECT_NAME')].name" --output text || true)
    if [ -n "$remaining_apis" ]; then
        print_warning "Some API Gateways may still exist:"
        echo "$remaining_apis"
    fi
}

# Main cleanup function
main() {
    # Check if AWS CLI is configured
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run: aws configure"
        exit 1
    fi
    
    # List resources
    list_resources
    
    # Confirm cleanup
    confirm_cleanup
    
    echo -e "${RED}🧹 Starting cleanup process...${NC}"
    
    # Perform cleanup
    cleanup_s3
    cleanup_lambda
    cleanup_api_gateway
    cleanup_iam
    cleanup_cloudwatch
    cleanup_local
    
    # Verify cleanup
    verify_cleanup
    
    echo ""
    echo -e "${GREEN}🎉 Cleanup completed!${NC}"
    echo ""
    echo -e "${BLUE}Summary:${NC}"
    echo "• All AWS resources with '$PROJECT_NAME' have been removed"
    echo "• Local deployment files have been cleaned up"
    echo "• Your AWS account is back to its original state"
    echo ""
    echo -e "${YELLOW}Note: It may take a few minutes for all changes to propagate.${NC}"
    echo -e "${BLUE}You can now run ./deploy-aws.sh again for a fresh deployment.${NC}"
}

# Run main function
main "$@"
