#!/bin/bash
# Backward compatibility wrapper for cleanup-aws.sh
# This script has been moved to scripts/cleanup-aws.sh

echo "⚠️  NOTICE: cleanup-aws.sh has been moved to scripts/cleanup-aws.sh"
echo "🔄 Redirecting to new location..."
echo ""

# Check if the new script exists
if [ -f "scripts/cleanup-aws.sh" ]; then
    exec ./scripts/cleanup-aws.sh "$@"
else
    echo "❌ Error: scripts/cleanup-aws.sh not found"
    echo "Please run: git pull origin main"
    exit 1
fi
