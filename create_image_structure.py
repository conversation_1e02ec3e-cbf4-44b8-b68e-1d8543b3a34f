#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the complete folder structure for EDA images
and generate a checklist for image creation progress.
"""

import os
import json

def create_folder_structure():
    """Create the complete folder structure for Website-Images2"""
    
    base_path = "Website-Images2"
    
    # Define the folder structure
    folders = [
        "About",
        "Contact", 
        "Home",
        "Services"
    ]
    
    # Create folders if they don't exist
    for folder in folders:
        folder_path = os.path.join(base_path, folder)
        os.makedirs(folder_path, exist_ok=True)
        print(f"Created/verified folder: {folder_path}")

def generate_image_checklist():
    """Generate a checklist of all images that need to be created"""
    
    images_needed = {
        "About": [
            "About-Hero-Transparent.jpg",
            "About-Hero.jpg", 
            "About-Our-Mission.jpg",
            "CTA.jpg"
        ],
        "Contact": [
            "Contact-Hero-Transparent.jpg",
            "Contact-Hero.jpg"
        ],
        "Home": [
            "CTA.jpg",
            "Home-About.jpg",
            "Home-Hero-Transparent.jpg", 
            "Home-Hero.jpg",
            "Home-Service-1.png",
            "Home-Service-2.png",
            "Home-Service-3.png",
            "Home-Testimonial.jpg"
        ],
        "Services": [
            "CTA.jpg",
            "Service-1.jpg",
            "Service-2.jpg", 
            "Service-3.jpg",
            "Services-Hero-Transparent.jpg",
            "Services-Hero.jpg"
        ]
    }
    
    # Check which images already exist
    checklist = {}
    total_images = 0
    completed_images = 0
    
    for folder, images in images_needed.items():
        checklist[folder] = {}
        for image in images:
            total_images += 1
            image_path = os.path.join("Website-Images2", folder, image)
            exists = os.path.exists(image_path)
            if exists:
                completed_images += 1
            checklist[folder][image] = {
                "exists": exists,
                "path": image_path,
                "status": "✅ Complete" if exists else "❌ Needed"
            }
    
    return checklist, total_images, completed_images

def print_checklist(checklist, total_images, completed_images):
    """Print a formatted checklist"""
    
    print(f"\n{'='*60}")
    print(f"EDA IMAGE GENERATION CHECKLIST")
    print(f"{'='*60}")
    print(f"Progress: {completed_images}/{total_images} images completed")
    print(f"{'='*60}\n")
    
    for folder, images in checklist.items():
        print(f"📁 {folder.upper()} SECTION:")
        print("-" * 40)
        
        for image_name, info in images.items():
            print(f"  {info['status']} {image_name}")
            if not info['exists']:
                # Map image to service type for context
                service_context = ""
                if "Service-1" in image_name or "Home-Service-1" in image_name:
                    service_context = " (De-embedding)"
                elif "Service-2" in image_name or "Home-Service-2" in image_name:
                    service_context = " (RF Design)"
                elif "Service-3" in image_name or "Home-Service-3" in image_name:
                    service_context = " (EM Simulation)"
                
                print(f"    → Path: {info['path']}{service_context}")
        print()

def save_checklist_json(checklist):
    """Save checklist as JSON for programmatic access"""
    
    with open("image_generation_checklist.json", "w") as f:
        json.dump(checklist, f, indent=2)
    print("📄 Checklist saved as 'image_generation_checklist.json'")

def main():
    """Main function to run the script"""
    
    print("🚀 Setting up EDA image generation structure...")
    
    # Create folder structure
    create_folder_structure()
    
    # Generate and display checklist
    checklist, total_images, completed_images = generate_image_checklist()
    print_checklist(checklist, total_images, completed_images)
    
    # Save checklist as JSON
    save_checklist_json(checklist)
    
    print(f"\n{'='*60}")
    print("NEXT STEPS:")
    print("1. Review the EDA_Image_Generation_Guide.md for detailed prompts")
    print("2. Use AI image generation tools with the provided prompts")
    print("3. Save generated images with exact filenames in correct folders")
    print("4. Run this script again to check progress")
    print("5. Update your website to use images from Website-Images2")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
