#!/usr/bin/env python3
"""
Batch runner for DALL-E image generation with selective options
"""

import os
import sys
from dalle_image_generator import EDAImageGenerator

def run_selective_generation():
    """Interactive script to generate specific images or retry failed ones"""
    
    print("🎨 EDA Image Generator - Batch Runner")
    print("=" * 50)
    
    # Check API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OpenAI API key not found!")
        print("Please set OPENAI_API_KEY environment variable")
        return
    
    generator = EDAImageGenerator(api_key)
    
    # Show options
    print("\nOptions:")
    print("1. Generate ALL images (20 total)")
    print("2. Generate only HOME section images (8 images)")
    print("3. Generate only SERVICE icons (3 images)")
    print("4. Generate only HERO images (6 images)")
    print("5. Retry failed/missing images only")
    print("6. Generate specific image by name")
    print("0. Exit")
    
    choice = input("\nEnter your choice (0-6): ").strip()
    
    if choice == "0":
        print("👋 Goodbye!")
        return
    elif choice == "1":
        print("🚀 Generating ALL images...")
        results = generator.generate_all_images()
        generator.print_summary(results)
    elif choice == "2":
        print("🏠 Generating HOME section images...")
        home_images = {k: v for k, v in generator.image_definitions.items() if k.startswith("Home/")}
        results = {}
        for path, config in home_images.items():
            results[path] = generator.generate_single_image(path, config)
        generator.print_summary(results)
    elif choice == "3":
        print("🔧 Generating SERVICE icon images...")
        service_images = {
            "Home/Home-Service-1.png": generator.image_definitions["Home/Home-Service-1.png"],
            "Home/Home-Service-2.png": generator.image_definitions["Home/Home-Service-2.png"], 
            "Home/Home-Service-3.png": generator.image_definitions["Home/Home-Service-3.png"]
        }
        results = {}
        for path, config in service_images.items():
            results[path] = generator.generate_single_image(path, config)
        generator.print_summary(results)
    elif choice == "4":
        print("🦸 Generating HERO images...")
        hero_images = {k: v for k, v in generator.image_definitions.items() if "Hero" in k}
        results = {}
        for path, config in hero_images.items():
            results[path] = generator.generate_single_image(path, config)
        generator.print_summary(results)
    elif choice == "5":
        print("🔄 Checking for missing images...")
        missing_images = {}
        for path, config in generator.image_definitions.items():
            full_path = generator.output_dir / path
            if not full_path.exists():
                missing_images[path] = config
        
        if not missing_images:
            print("✅ All images already exist!")
        else:
            print(f"Found {len(missing_images)} missing images. Generating...")
            results = {}
            for path, config in missing_images.items():
                results[path] = generator.generate_single_image(path, config)
            generator.print_summary(results)
    elif choice == "6":
        print("\nAvailable images:")
        for i, path in enumerate(generator.image_definitions.keys(), 1):
            print(f"{i:2d}. {path}")
        
        try:
            img_choice = int(input(f"\nEnter image number (1-{len(generator.image_definitions)}): "))
            if 1 <= img_choice <= len(generator.image_definitions):
                path = list(generator.image_definitions.keys())[img_choice - 1]
                config = generator.image_definitions[path]
                print(f"🎨 Generating: {path}")
                success = generator.generate_single_image(path, config)
                if success:
                    print("✅ Generation successful!")
                else:
                    print("❌ Generation failed!")
            else:
                print("❌ Invalid choice!")
        except ValueError:
            print("❌ Please enter a valid number!")
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    run_selective_generation()
