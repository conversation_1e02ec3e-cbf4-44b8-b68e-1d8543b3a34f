#!/usr/bin/env python3
"""
Automated DALL-E Image Generator for EDA Website
This script automates the generation of EDA-related images using OpenAI's DALL-E API.
"""

import os
import json
import time
import requests
from typing import Dict, List, Tuple
from pathlib import Path

class EDAImageGenerator:
    def __init__(self, api_key: str = None):
        """Initialize the image generator with OpenAI API key"""
        # Option 1: Use environment variable (recommended)
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')

        # Option 2: Uncomment and add your key directly (less secure)
        # self.api_key = api_key or "sk-proj-your-actual-key-here"

        if not self.api_key:
            print("⚠️  OpenAI API key not found. Please set OPENAI_API_KEY environment variable.")
            print("   Or pass it as a parameter when creating the generator.")
            print("   Or uncomment the direct key line in the __init__ method.")

        self.base_url = "https://api.openai.com/v1/images/generations"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # Create output directory
        self.output_dir = Path("Website-Images2")
        self.output_dir.mkdir(exist_ok=True)

        # Initialize image definitions
        self.image_definitions = self._define_images()

    def _define_images(self) -> Dict[str, Dict]:
        """Define all images with their prompts and specifications"""
        return {
            # HOME SECTION
            "Home/Home-Hero.jpg": {
                "prompt": "Modern electronics engineering laboratory with computer workstations, circuit design software on monitors, professional workspace, clean lighting, high-tech environment",
                "size": "1792x1024",
                "style": "natural"
            },
            "Home/Home-Hero-Transparent.jpg": {
                "prompt": "Modern electronics engineering laboratory with computer workstations, circuit design software on monitors, professional workspace, clean lighting",
                "size": "1792x1024",
                "style": "natural"
            },
            "Home/Home-About.jpg": {
                "prompt": "Professional engineering office with multiple computer monitors showing electronic circuit designs, modern workspace, collaborative environment",
                "size": "1024x1024",
                "style": "natural"
            },
            "Home/Home-Service-1.png": {
                "prompt": "Vector network analyzer measurement equipment, RF test setup, electronic measurement instruments, professional laboratory equipment, clean background",
                "size": "1024x1024",
                "style": "natural"
            },
            "Home/Home-Service-2.png": {
                "prompt": "Printed circuit board design layout, RF components, electronic circuit patterns, professional technical illustration, clean design",
                "size": "1024x1024",
                "style": "natural"
            },
            "Home/Home-Service-3.png": {
                "prompt": "Electromagnetic field simulation visualization, colorful wave patterns, antenna design, technical simulation graphics, professional illustration",
                "size": "1024x1024",
                "style": "natural"
            },
            "Home/Home-Testimonial.jpg": {
                "prompt": "Professional office setting, clean modern atmosphere, soft lighting, business environment, minimal background",
                "size": "1024x1024",
                "style": "natural"
            },
            "Home/CTA.jpg": {
                "prompt": "Circuit board patterns, blue gradient background, professional design, clean minimalist style",
                "size": "1024x1024",
                "style": "natural"
            },

            # ABOUT SECTION
            "About/About-Hero.jpg": {
                "prompt": "Electronics laboratory with oscilloscopes, computer workstations, circuit design monitors, professional lighting, high-tech environment",
                "size": "1792x1024",
                "style": "natural"
            },
            "About/About-Hero-Transparent.jpg": {
                "prompt": "Electronics laboratory with oscilloscopes, computer workstations, circuit design monitors, professional lighting",
                "size": "1792x1024",
                "style": "natural"
            },
            "About/About-Our-Mission.jpg": {
                "prompt": "Abstract circuit patterns, interconnected technology design, blue and green colors, modern professional background",
                "size": "1024x1024",
                "style": "natural"
            },
            "About/CTA.jpg": {
                "prompt": "Circuit board patterns background, blue gradient, clean minimalist design, professional style",
                "size": "1024x1024",
                "style": "natural"
            },

            # CONTACT SECTION
            "Contact/Contact-Hero.jpg": {
                "prompt": "Professional meeting room with large displays showing circuit designs, modern conference room, business atmosphere",
                "size": "1792x1024",
                "style": "natural"
            },
            "Contact/Contact-Hero-Transparent.jpg": {
                "prompt": "Professional meeting room with large displays showing circuit designs, modern conference room",
                "size": "1792x1024",
                "style": "natural"
            },

            # SERVICES SECTION
            "Services/Services-Hero.jpg": {
                "prompt": "Electronics workspace with measurement equipment, computer workstations, professional laboratory environment",
                "size": "1792x1024",
                "style": "natural"
            },
            "Services/Services-Hero-Transparent.jpg": {
                "prompt": "Electronics workspace with measurement equipment, computer workstations, professional laboratory",
                "size": "1792x1024",
                "style": "natural"
            },
            "Services/Service-1.jpg": {
                "prompt": "RF measurement laboratory, vector network analyzer, test equipment, professional measurement setup",
                "size": "1024x1024",
                "style": "natural"
            },
            "Services/Service-2.jpg": {
                "prompt": "PCB design software interface, circuit layout design, electronic components, professional workstation",
                "size": "1024x1024",
                "style": "natural"
            },
            "Services/Service-3.jpg": {
                "prompt": "Electromagnetic simulation visualization, antenna patterns, colorful field distribution, professional simulation",
                "size": "1024x1024",
                "style": "natural"
            },
            "Services/CTA.jpg": {
                "prompt": "RF circuits and measurement equipment, professional gradient background, modern corporate design",
                "size": "1024x1024",
                "style": "natural"
            },

            # NEW IMAGE - Services Hero 2
            "Services/Services-Hero2.jpg": {
                "prompt": "online EDA service focus on de-embedding, RF design, and EM simulation",
                "size": "1792x1024",
                "style": "natural"
            }
        }

    def generate_single_image(self, image_path: str, image_config: Dict) -> bool:
        """Generate a single image using DALL-E API"""
        try:
            print(f"🎨 Generating: {image_path}")
            print(f"   Prompt: {image_config['prompt'][:100]}...")

            # Clean and validate prompt
            prompt = image_config['prompt'].strip()
            if len(prompt) > 1000:
                prompt = prompt[:1000]
                print(f"   ⚠️  Prompt truncated to 1000 characters")

            # Prepare API request with error handling
            payload = {
                "model": "dall-e-3",
                "prompt": prompt,
                "n": 1,
                "size": image_config['size'],
                "quality": "standard"
            }

            # Only add style if it's valid for DALL-E 3
            if image_config.get('style') in ['natural', 'vivid']:
                payload['style'] = image_config['style']

            print(f"   📤 Making API request...")

            # Make API request
            response = requests.post(self.base_url, headers=self.headers, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                image_url = result['data'][0]['url']

                print(f"   📥 Downloading image...")
                # Download the image
                img_response = requests.get(image_url, timeout=60)
                if img_response.status_code == 200:
                    # Create directory if it doesn't exist
                    full_path = self.output_dir / image_path
                    full_path.parent.mkdir(parents=True, exist_ok=True)

                    # Save the image
                    with open(full_path, 'wb') as f:
                        f.write(img_response.content)

                    print(f"   ✅ Saved: {full_path}")
                    return True
                else:
                    print(f"   ❌ Failed to download image: {img_response.status_code}")
                    return False
            else:
                error_details = ""
                try:
                    error_json = response.json()
                    if 'error' in error_json and 'message' in error_json['error']:
                        error_details = error_json['error']['message']
                    else:
                        error_details = str(error_json)
                except:
                    error_details = response.text

                print(f"   ❌ API Error: {response.status_code}")
                print(f"   📝 Details: {error_details}")

                # Suggest fixes for common errors
                if response.status_code == 400:
                    print(f"   💡 Try: Simplifying the prompt or checking API key")
                elif response.status_code == 429:
                    print(f"   💡 Try: Waiting longer between requests")

                return False

        except requests.exceptions.Timeout:
            print(f"   ❌ Request timed out")
            return False
        except Exception as e:
            print(f"   ❌ Error generating {image_path}: {str(e)}")
            return False

    def generate_all_images(self, delay_seconds: int = 2) -> Dict[str, bool]:
        """Generate all images with delay between requests"""
        results = {}
        total_images = len(self.image_definitions)

        print(f"🚀 Starting generation of {total_images} EDA images...")
        print(f"⏱️  Using {delay_seconds}s delay between requests")
        print("=" * 60)

        for i, (image_path, config) in enumerate(self.image_definitions.items(), 1):
            print(f"\n[{i}/{total_images}] Processing: {image_path}")

            # Check if image already exists
            full_path = self.output_dir / image_path
            if full_path.exists():
                print(f"   ⏭️  Already exists, skipping...")
                results[image_path] = True
                continue

            # Generate the image
            success = self.generate_single_image(image_path, config)
            results[image_path] = success

            # Add delay between requests (except for last image)
            if i < total_images and success:
                print(f"   ⏳ Waiting {delay_seconds}s before next request...")
                time.sleep(delay_seconds)

        return results

    def print_summary(self, results: Dict[str, bool]):
        """Print generation summary"""
        successful = sum(1 for success in results.values() if success)
        total = len(results)

        print("\n" + "=" * 60)
        print("GENERATION SUMMARY")
        print("=" * 60)
        print(f"✅ Successful: {successful}/{total}")
        print(f"❌ Failed: {total - successful}/{total}")

        if successful < total:
            print("\nFailed images:")
            for path, success in results.items():
                if not success:
                    print(f"  ❌ {path}")

        print(f"\n📁 Images saved to: {self.output_dir.absolute()}")

def setup_api_key():
    """Interactive setup for API key"""
    print("🔑 OpenAI API Key Setup")
    print("=" * 30)

    # Check if already set
    existing_key = os.getenv('OPENAI_API_KEY')
    if existing_key:
        print(f"✅ API key already found: {existing_key[:10]}...")
        use_existing = input("Use existing key? (y/n): ").lower().strip()
        if use_existing == 'y':
            return existing_key

    print("\n📝 Please enter your OpenAI API key:")
    print("   (Get it from: https://platform.openai.com/api-keys)")
    api_key = input("API Key: ").strip()

    if not api_key:
        print("❌ No API key provided!")
        return None

    if not api_key.startswith('sk-'):
        print("⚠️  Warning: API key should start with 'sk-'")
        confirm = input("Continue anyway? (y/n): ").lower().strip()
        if confirm != 'y':
            return None

    # Set environment variable for this session
    os.environ['OPENAI_API_KEY'] = api_key
    print("✅ API key set for this session!")

    return api_key

def main():
    """Main function to run the image generator"""
    print("🎨 EDA Image Generator for DALL-E")
    print("=" * 50)

    # Setup API key interactively
    api_key = setup_api_key()
    if not api_key:
        print("❌ Cannot proceed without API key!")
        return

    print(f"\n💰 Cost estimate: ~$0.80 for 20 images")
    proceed = input("Proceed with image generation? (y/n): ").lower().strip()
    if proceed != 'y':
        print("👋 Generation cancelled!")
        return

    # Initialize generator
    generator = EDAImageGenerator(api_key)

    # Generate all images
    results = generator.generate_all_images(delay_seconds=3)

    # Print summary
    generator.print_summary(results)

if __name__ == "__main__":
    main()
