#!/bin/bash

# 🚀 EDA Easy Website - Automated AWS Deployment Script
# This script automates the complete deployment of website + Python backend to AWS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="eda-easy"
BUCKET_NAME="${PROJECT_NAME}-website-$(date +%s)"  # Unique bucket name
LAMBDA_FUNCTION_NAME="${PROJECT_NAME}-backend"
API_NAME="${PROJECT_NAME}-api"
REGION=$(aws configure get region || echo "us-east-1")

echo -e "${BLUE}🚀 Starting EDA Easy AWS Deployment...${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
echo -e "${BLUE}📋 Checking prerequisites...${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first:"
    echo "curl 'https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip' -o 'awscliv2.zip'"
    echo "unzip awscliv2.zip && sudo ./aws/install"
    exit 1
fi

# Check if AWS is configured
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS CLI is not configured. Please run: aws configure"
    exit 1
fi

# Check if required files exist
required_files=(
    "index.html"
    "about.html"
    "services.html"
    "pricing.html"
    "contact.html"
    "de-embedding-trial.html"
    "web_s21_backend.py"
    "css"
    "js"
    "Website-Images2"
)

for file in "${required_files[@]}"; do
    if [ ! -e "$file" ]; then
        print_error "Required file/folder not found: $file"
        exit 1
    fi
done

print_status "All prerequisites met!"

# Step 1: Create S3 bucket for website
echo -e "${BLUE}🪣 Creating S3 bucket for website...${NC}"

aws s3 mb s3://$BUCKET_NAME --region $REGION
print_status "S3 bucket created: $BUCKET_NAME"

# Configure bucket for static website hosting
aws s3 website s3://$BUCKET_NAME --index-document index.html --error-document index.html
print_status "Static website hosting enabled"

# Remove public access block first
echo -e "${BLUE}🔓 Removing public access block...${NC}"
aws s3api delete-public-access-block --bucket $BUCKET_NAME
print_status "Public access block removed"

# Set bucket policy for public access
cat > bucket-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::$BUCKET_NAME/*"
    }
  ]
}
EOF

aws s3api put-bucket-policy --bucket $BUCKET_NAME --policy file://bucket-policy.json
print_status "Bucket policy applied for public access"

# Step 2: Upload website files
echo -e "${BLUE}📤 Uploading website files...${NC}"

# Upload all files with proper content types
aws s3 sync . s3://$BUCKET_NAME \
    --exclude "*.sh" \
    --exclude "*.py" \
    --exclude "*.md" \
    --exclude ".git/*" \
    --exclude "*.json" \
    --exclude "snpfiles/*" \
    --exclude "__pycache__/*" \
    --exclude "*.pyc" \
    --delete

print_status "Website files uploaded successfully"

# Get website URL
WEBSITE_URL="http://$BUCKET_NAME.s3-website-$REGION.amazonaws.com"
echo -e "${GREEN}🌐 Website URL: $WEBSITE_URL${NC}"

# Step 3: Prepare Lambda function
echo -e "${BLUE}🐍 Preparing Lambda function...${NC}"

# Create deployment directory
mkdir -p lambda-deployment
cd lambda-deployment

# Copy and modify backend file
cp ../web_s21_backend.py lambda_function.py

# Create Lambda handler wrapper
cat > lambda_handler.py << 'EOF'
import json
import numpy as np
import tempfile
import os
import re
from io import StringIO

def lambda_handler(event, context):
    """AWS Lambda entry point for EDA backend"""

    # Enable CORS
    headers = {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    }

    try:
        # Handle OPTIONS request (CORS preflight)
        if event.get('httpMethod') == 'OPTIONS':
            return {
                'statusCode': 200,
                'headers': headers,
                'body': ''
            }

        # Health check endpoint
        if event.get('httpMethod') == 'GET' and '/health' in event.get('path', ''):
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({'status': 'healthy', 'service': 'EDA Backend'})
            }

        # Demo files endpoint
        if event.get('httpMethod') == 'GET' and '/demo-files' in event.get('path', ''):
            demo_data = {
                'dut': {
                    'frequencies': list(np.linspace(3.4, 4.4, 100)),
                    's21_db': list(-20 * np.log10(np.linspace(0.1, 1.0, 100))),
                    'filename': 'demo_dut.s2p',
                    'success': True
                },
                'fixture': {
                    'frequencies': list(np.linspace(3.4, 4.4, 100)),
                    's21_db': list(-10 * np.log10(np.linspace(0.8, 1.0, 100))),
                    'filename': 'demo_fixture.s2p',
                    'success': True
                }
            }
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps(demo_data)
            }

        # Default response
        return {
            'statusCode': 404,
            'headers': headers,
            'body': json.dumps({'error': 'Endpoint not found'})
        }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': str(e)})
        }
EOF

# Install dependencies
pip3 install numpy -t .

# Create deployment package
zip -r ../lambda-deployment.zip . -x "*.pyc" "__pycache__/*"

cd ..
print_status "Lambda deployment package created"

# Step 4: Create Lambda function
echo -e "${BLUE}⚡ Creating Lambda function...${NC}"

# Create Lambda function
aws lambda create-function \
    --function-name $LAMBDA_FUNCTION_NAME \
    --runtime python3.9 \
    --role arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/lambda-execution-role \
    --handler lambda_handler.lambda_handler \
    --zip-file fileb://lambda-deployment.zip \
    --timeout 30 \
    --memory-size 256 \
    --region $REGION || {

    # If role doesn't exist, create it
    print_warning "Creating Lambda execution role..."

    # Create trust policy
    cat > trust-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

    # Create IAM role
    aws iam create-role \
        --role-name lambda-execution-role \
        --assume-role-policy-document file://trust-policy.json

    # Attach basic execution policy
    aws iam attach-role-policy \
        --role-name lambda-execution-role \
        --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

    print_status "Lambda execution role created"

    # Wait for role to be available
    sleep 10

    # Try creating function again
    aws lambda create-function \
        --function-name $LAMBDA_FUNCTION_NAME \
        --runtime python3.9 \
        --role arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/lambda-execution-role \
        --handler lambda_handler.lambda_handler \
        --zip-file fileb://lambda-deployment.zip \
        --timeout 30 \
        --memory-size 256 \
        --region $REGION
}

print_status "Lambda function created: $LAMBDA_FUNCTION_NAME"

# Step 5: Create API Gateway
echo -e "${BLUE}🌐 Creating API Gateway...${NC}"

# Create REST API
API_ID=$(aws apigateway create-rest-api \
    --name $API_NAME \
    --description "EDA Easy Backend API" \
    --query 'id' \
    --output text)

print_status "API Gateway created: $API_ID"

# Get root resource ID
ROOT_RESOURCE_ID=$(aws apigateway get-resources \
    --rest-api-id $API_ID \
    --query 'items[0].id' \
    --output text)

# Create /api resource
API_RESOURCE_ID=$(aws apigateway create-resource \
    --rest-api-id $API_ID \
    --parent-id $ROOT_RESOURCE_ID \
    --path-part api \
    --query 'id' \
    --output text)

# Create /{proxy+} resource under /api
PROXY_RESOURCE_ID=$(aws apigateway create-resource \
    --rest-api-id $API_ID \
    --parent-id $API_RESOURCE_ID \
    --path-part '{proxy+}' \
    --query 'id' \
    --output text)

# Create ANY method
aws apigateway put-method \
    --rest-api-id $API_ID \
    --resource-id $PROXY_RESOURCE_ID \
    --http-method ANY \
    --authorization-type NONE

# Set up Lambda integration
LAMBDA_ARN="arn:aws:lambda:$REGION:$(aws sts get-caller-identity --query Account --output text):function:$LAMBDA_FUNCTION_NAME"

aws apigateway put-integration \
    --rest-api-id $API_ID \
    --resource-id $PROXY_RESOURCE_ID \
    --http-method ANY \
    --type AWS_PROXY \
    --integration-http-method POST \
    --uri "arn:aws:apigateway:$REGION:lambda:path/2015-03-31/functions/$LAMBDA_ARN/invocations"

# Give API Gateway permission to invoke Lambda
aws lambda add-permission \
    --function-name $LAMBDA_FUNCTION_NAME \
    --statement-id api-gateway-invoke \
    --action lambda:InvokeFunction \
    --principal apigateway.amazonaws.com \
    --source-arn "arn:aws:execute-api:$REGION:$(aws sts get-caller-identity --query Account --output text):$API_ID/*/*" \
    --region $REGION

# Deploy API
aws apigateway create-deployment \
    --rest-api-id $API_ID \
    --stage-name prod

API_URL="https://$API_ID.execute-api.$REGION.amazonaws.com/prod"
print_status "API Gateway deployed: $API_URL"

# Step 6: Update frontend with API URL
echo -e "${BLUE}🔧 Updating frontend configuration...${NC}"

# Update trial.js with the new API URL
sed -i.bak "s|const API_BASE_URL = 'http://127.0.0.1:5002';|const API_BASE_URL = '$API_URL';|g" js/trial.js

# Re-upload the updated JavaScript file
aws s3 cp js/trial.js s3://$BUCKET_NAME/js/trial.js

print_status "Frontend updated with API URL"

# Cleanup
rm -rf lambda-deployment lambda-deployment.zip bucket-policy.json trust-policy.json js/trial.js.bak

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Deployment Summary:${NC}"
echo -e "Website URL: ${GREEN}$WEBSITE_URL${NC}"
echo -e "API URL: ${GREEN}$API_URL${NC}"
echo -e "S3 Bucket: ${GREEN}$BUCKET_NAME${NC}"
echo -e "Lambda Function: ${GREEN}$LAMBDA_FUNCTION_NAME${NC}"
echo -e "API Gateway: ${GREEN}$API_ID${NC}"
echo ""
echo -e "${YELLOW}⏳ Note: It may take a few minutes for all services to be fully available.${NC}"
