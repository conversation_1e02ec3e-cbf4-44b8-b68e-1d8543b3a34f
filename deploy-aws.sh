#!/bin/bash
# Backward compatibility wrapper for deploy-aws.sh
# This script has been moved to scripts/deploy-aws.sh

echo "⚠️  NOTICE: deploy-aws.sh has been moved to scripts/deploy-aws.sh"
echo "🔄 Redirecting to new location..."
echo ""

# Check if the new script exists
if [ -f "scripts/deploy-aws.sh" ]; then
    exec ./scripts/deploy-aws.sh "$@"
else
    echo "❌ Error: scripts/deploy-aws.sh not found"
    echo "Please run: git pull origin main"
    exit 1
fi
