# 🚀 AWS Deployment Guide

## 🎯 **Quick Deploy - Get Live in 10 Minutes**

### **Prerequisites**
- AWS Account created
- AWS CLI installed and configured
- Local development working (see [Local Development Guide](LOCAL_DEVELOPMENT_GUIDE.md))

### **1. Configure AWS CLI**
```bash
# Install AWS CLI (if not installed)
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure with your credentials
aws configure
# AWS Access Key ID: [Your Access Key]
# AWS Secret Access Key: [Your Secret Key]
# Default region name: us-east-1
# Default output format: json
```

### **2. Deploy to AWS**
```bash
# Full deployment (first time)
./scripts/deploy-aws.sh

# Wait for completion (5-10 minutes)
# Script will output your website URL at the end
```

### **3. Test Your Deployment**
```bash
# Automated testing
./scripts/test-deployment.sh

# Manual verification
# Visit the URL provided by deploy-aws.sh
# Test S21 functionality on the live site
```

---

## 📋 **Deployment Scenarios**

### **🏗️ First Time Setup**
```bash
# Complete AWS infrastructure setup
./scripts/deploy-aws.sh

# Expected output:
# ✅ S3 bucket created
# ✅ Website files uploaded
# ✅ Lambda function deployed
# ✅ API Gateway configured
# 🌐 Website URL: http://eda-easy-website-1748501958.s3-website-us-west-2.amazonaws.com/index.html
# 🔗 API URL: https://your-api-id.execute-api.us-east-1.amazonaws.com/prod
```

### **🔄 Update Website Content**
```bash
# Changed HTML, CSS, JavaScript, or images?
./scripts/sync-to-aws.sh

# This is much faster than full deployment (30 seconds vs 10 minutes)
```

### **🔧 Update Backend Logic**
```bash
# Changed Python Lambda functions?
./scripts/update-lambda.sh

# Or manually:
zip lambda.zip lambda_no_numpy.py
aws lambda update-function-code --function-name eda-easy-backend --zip-file fileb://lambda.zip
```

### **🧪 Test Everything**
```bash
# Comprehensive testing
./scripts/test-deployment.sh

# Manual testing checklist:
# ✅ Website loads correctly
# ✅ All pages accessible
# ✅ S21 processing works
# ✅ API endpoints respond
```

### **🗑️ Clean Slate (Nuclear Option)**
```bash
# Remove everything and start over
./scripts/cleanup-aws.sh
./scripts/deploy-aws.sh
```

---

## 🏗️ **AWS Architecture Overview**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CloudFront    │    │    S3 Bucket     │    │  Lambda Function│
│   (Optional)    │───▶│  Static Website  │    │  Python Backend │
│   CDN/HTTPS     │    │  HTML/CSS/JS     │    │  S21 Processing │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        ▲
                                │                        │
                                ▼                        │
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Web Browser    │───▶│  API Gateway    │
                       │   User Access    │    │  REST API       │
                       └──────────────────┘    └─────────────────┘
```

### **Components Deployed:**
1. **S3 Bucket**: Hosts static website files (HTML, CSS, JS, images)
2. **Lambda Function**: Processes S21 files and handles backend logic
3. **API Gateway**: Provides REST API endpoints for frontend-backend communication
4. **IAM Roles**: Security permissions for Lambda execution

---

## 🔧 **Configuration Details**

### **S3 Bucket Settings**
- **Name**: `eda-easy-website-{timestamp}`
- **Region**: `us-east-1` (cheapest)
- **Public Access**: Enabled for website hosting
- **Static Website Hosting**: Enabled
- **Index Document**: `index.html`

### **Lambda Function Settings**
- **Name**: `eda-easy-backend`
- **Runtime**: `python3.9`
- **Memory**: `256 MB`
- **Timeout**: `30 seconds`
- **Handler**: `lambda_handler.lambda_handler`

### **API Gateway Settings**
- **Name**: `eda-easy-api`
- **Type**: REST API
- **Stage**: `prod`
- **CORS**: Enabled for all origins

---

## 🔍 **Troubleshooting Deployment Issues**

### **AWS CLI Not Configured**
```bash
# Error: Unable to locate credentials
aws configure

# Verify configuration
aws sts get-caller-identity
```

### **S3 Bucket Name Conflicts**
```bash
# Error: Bucket name already exists
# Solution: Script automatically generates unique names with timestamp
# If still fails, edit deploy-aws.sh and change BUCKET_NAME
```

### **Lambda Deployment Fails**
```bash
# Error: Role does not exist
# Solution: Script automatically creates IAM role
# If fails, check IAM permissions in AWS Console

# Manual role creation:
aws iam create-role --role-name lambda-execution-role --assume-role-policy-document file://trust-policy.json
```

### **API Gateway Issues**
```bash
# Error: API not responding
# Check API Gateway logs in AWS Console
# Verify Lambda function is working:
aws lambda invoke --function-name eda-easy-backend --payload '{}' response.json
cat response.json
```

### **Website Not Loading**
```bash
# Check S3 bucket policy
aws s3api get-bucket-policy --bucket your-bucket-name

# Verify files uploaded
aws s3 ls s3://your-bucket-name/

# Test direct S3 URL
curl -I https://your-bucket.s3-website-us-east-1.amazonaws.com
```

---

## 💰 **Cost Estimation**

### **Monthly AWS Costs (Typical Usage)**
- **S3 Storage**: $0.50-2.00 (depending on file sizes)
- **S3 Requests**: $0.10-0.50 (depending on traffic)
- **Lambda Execution**: $0.20-1.00 (depending on S21 processing)
- **API Gateway**: $0.35-3.50 (per million requests)
- **Data Transfer**: $0.09/GB (outbound)

**Total Estimated Cost**: $3-10/month for small to medium business usage

### **Free Tier Benefits (First 12 Months)**
- S3: 5GB storage, 20,000 GET requests
- Lambda: 1M requests, 400,000 GB-seconds
- API Gateway: 1M requests
- Data Transfer: 1GB outbound

---

## 🔒 **Security Best Practices**

### **IAM Permissions**
- Use least privilege principle
- Lambda execution role has minimal required permissions
- No hardcoded credentials in code

### **S3 Security**
- Public read access only for website files
- No public write access
- Bucket policy restricts to specific actions

### **API Security**
- CORS properly configured
- No sensitive data in API responses
- Rate limiting through API Gateway

---

## 📊 **Monitoring and Maintenance**

### **Check Deployment Health**
```bash
# Automated health check
./scripts/test-deployment.sh

# Manual checks
curl -I https://your-website-url.com
curl https://your-api-url.com/health
```

### **View Logs**
```bash
# Lambda function logs
aws logs describe-log-groups --log-group-name-prefix /aws/lambda/eda-easy-backend

# API Gateway logs (if enabled)
aws logs describe-log-groups --log-group-name-prefix API-Gateway-Execution-Logs
```

### **Update Deployment**
```bash
# Regular updates
./scripts/sync-to-aws.sh

# Backend updates
./scripts/update-lambda.sh

# Full redeployment (if needed)
./scripts/cleanup-aws.sh && ./scripts/deploy-aws.sh
```

---

## 🆘 **Emergency Procedures**

### **Site Down - Quick Recovery**
```bash
# 1. Check if it's a temporary AWS issue
aws s3 ls s3://your-bucket-name/

# 2. Redeploy if needed
./scripts/deploy-aws.sh

# 3. Rollback to previous version (if you have backups)
aws s3 sync s3://your-backup-bucket/ s3://your-main-bucket/
```

### **Complete Infrastructure Recovery**
```bash
# Nuclear option - rebuild everything
./scripts/cleanup-aws.sh
./scripts/deploy-aws.sh
./scripts/test-deployment.sh
```

---

## 📞 **Support Resources**

- **AWS Documentation**: https://docs.aws.amazon.com/
- **AWS Support**: https://aws.amazon.com/support/
- **Local Issues**: See [Local Development Guide](LOCAL_DEVELOPMENT_GUIDE.md)
- **Project Issues**: Check `docs/ISSUE_ANALYSIS.md`
