# 🛠️ Local Development Guide

## 🎯 **Quick Start - Get Running in 5 Minutes**

### **Prerequisites**
- Python 3.8+ installed
- Git installed
- AWS CLI installed (for deployment)
- Text editor (VS Code recommended)

### **1. Clone and Setup**
```bash
# Clone the repository
git clone <your-repo-url>
cd website_joshua

# Install Python dependencies
pip3 install -r requirements.txt
pip3 install -r snp_requirements.txt
```

### **2. Start Local Development Server**
```bash
# Option A: Simple HTTP server for frontend only
cd .
python3 -m http.server 8000

# Option B: Full backend with S21 processing
./scripts/start_web_backend.sh
```

### **3. Access Your Local Site**
- **Frontend Only**: http://localhost:8000
- **With Backend**: http://localhost:5002
- **S21 Demo Page**: http://localhost:8000/simple_s21_test.html

---

## 📁 **Project Structure Overview**

```
website_joshua/
├── 📄 Frontend Files
│   ├── *.html                 # Website pages
│   ├── css/                   # Stylesheets
│   ├── js/                    # JavaScript files
│   └── img/                   # Images and assets
│
├── 🐍 Backend Files
│   ├── *_lambda.py            # AWS Lambda functions
│   ├── rf_process7.py         # Local S21 processing
│   ├── ntwk_1.py             # S-parameter utilities
│   └── snp_*.py              # SNP file processors
│
├── 🚀 Scripts (Automation)
│   ├── deploy-aws.sh         # Full AWS deployment
│   ├── sync-to-aws.sh        # Sync changes to AWS
│   ├── cleanup-aws.sh        # Remove AWS resources
│   ├── setup_*.sh            # Environment setup
│   └── fix-*.sh              # Issue resolution
│
├── 📚 Documentation
│   ├── docs/                 # All documentation
│   └── README.md             # Project overview
│
└── 📦 Dependencies
    ├── requirements.txt      # Python packages
    └── snp_requirements.txt  # S-parameter packages
```

---

## 🔧 **Development Workflows**

### **Frontend Development**
```bash
# 1. Start simple server
python3 -m http.server 8000

# 2. Edit HTML/CSS/JS files
# 3. Refresh browser to see changes
# 4. No restart needed for frontend changes
```

### **Backend Development (S21 Processing)**
```bash
# 1. Start backend server
./scripts/start_web_backend.sh

# 2. Test S21 functionality
open http://localhost:5002/simple_s21_test.html

# 3. Edit Python files (rf_process7.py, etc.)
# 4. Restart server to see changes
```

### **Testing SNP File Processing**
```bash
# Test with sample files
python3 snp_processor.py snpfiles/SfFilterSb.s2p

# Test web interface
python3 snp_server.py
# Then visit: http://localhost:5000/skrf_s21_demo.html
```

---

## 🧪 **Testing Your Changes**

### **1. Frontend Testing**
```bash
# Test all pages load correctly
./scripts/test-website.sh

# Manual testing checklist:
# ✅ All pages load without errors
# ✅ Navigation works
# ✅ Images display correctly
# ✅ Mobile responsive design
```

### **2. Backend Testing**
```bash
# Test S21 processing
python3 -c "
import rf_process7
result = rf_process7.process_snp_file('snpfiles/SfFilterSb.s2p')
print('✅ Backend working' if result else '❌ Backend failed')
"

# Test Lambda function locally
python3 lambda_no_numpy.py
```

### **3. Integration Testing**
```bash
# Test full stack locally
./scripts/start_web_backend.sh &
sleep 3
curl http://localhost:5002/health || echo "❌ Backend not responding"
curl http://localhost:8000 || echo "❌ Frontend not responding"
```

---

## 🔍 **Debugging Common Issues**

### **Port Already in Use**
```bash
# Find what's using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>

# Or use different port
python3 -m http.server 8080
```

### **Python Dependencies Missing**
```bash
# Install all dependencies
pip3 install -r requirements.txt
pip3 install -r snp_requirements.txt

# Check specific package
python3 -c "import flask; print('✅ Flask installed')"
python3 -c "import skrf; print('✅ scikit-rf installed')"
```

### **SNP File Processing Errors**
```bash
# Test with known good file
python3 snp_processor.py snpfiles/SfFilterSb.s2p

# Check file format
head -5 snpfiles/SfFilterSb.s2p

# Verify scikit-rf installation
python3 -c "import skrf; print(f'scikit-rf version: {skrf.__version__}')"
```

---

## 🚀 **Ready to Deploy?**

Once your local development is working:

1. **Test everything locally first**
2. **Commit your changes to git**
3. **Follow the [AWS Deployment Guide](AWS_DEPLOYMENT_GUIDE.md)**

### **Quick Deploy Commands**
```bash
# First time deployment
./scripts/deploy-aws.sh

# Update existing deployment
./scripts/sync-to-aws.sh

# Test deployment
./scripts/test-deployment.sh
```

---

## 💡 **Pro Tips**

### **Development Efficiency**
- Use VS Code with Live Server extension for auto-refresh
- Keep browser dev tools open to catch JavaScript errors
- Use `python3 -m http.server 8000` for quick frontend testing
- Use `./scripts/start_web_backend.sh` for full-stack development

### **File Organization**
- Keep HTML files in root for simple hosting
- Put reusable code in separate Python modules
- Use `snpfiles/` for test S-parameter files
- Document any new scripts you create

### **Version Control**
```bash
# Before making changes
git checkout -b feature/your-feature-name

# After testing locally
git add .
git commit -m "Description of changes"
git push origin feature/your-feature-name
```

---

## 📞 **Need Help?**

- **Documentation**: Check `docs/` folder for specific guides
- **Issues**: Look at `docs/ISSUE_ANALYSIS.md` for common problems
- **AWS Problems**: See `docs/AWS_DEPLOYMENT_GUIDE.md`
- **S21 Issues**: Check `docs/S21_Plotting_Solution.md`
