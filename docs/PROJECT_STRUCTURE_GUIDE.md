# 📁 Project Structure Guide

## 🎯 **New Organized Structure**

After reorganization, the project now has a cleaner, more maintainable structure:

```
website_joshua/
├── 📄 Frontend (Website Files)
│   ├── *.html                    # Main website pages
│   ├── css/                      # Stylesheets
│   │   ├── style.css            # Main styles
│   │   ├── trial.css            # S21 demo styles
│   │   └── reset.css            # CSS reset
│   ├── js/                       # JavaScript files
│   │   ├── script.js            # Main website JS
│   │   ├── trial.js             # S21 processing JS
│   │   └── snp-parser.js        # SNP file parsing
│   └── img/                      # Images and assets
│       ├── favicon.png
│       ├── home/                # Homepage images
│       ├── about/               # About page images
│       ├── services/            # Services page images
│       └── contact/             # Contact page images
│
├── 🐍 Backend (Python Files)
│   ├── lambda_no_numpy.py       # ✅ Working Lambda (v10.0)
│   ├── rf_process7.py           # ✅ Local S21 processing
│   ├── ntwk_1.py               # ✅ S-parameter utilities
│   ├── snp_processor.py        # SNP file processor
│   ├── snp_server.py           # SNP processing server
│   ├── simple_server.py        # Simple test server
│   └── *_lambda.py             # Other Lambda versions
│
├── 🚀 Scripts (All Automation)
│   ├── 📋 README.md             # Script documentation
│   ├── 🏗️ Deployment Scripts
│   │   ├── deploy-aws.sh        # Full AWS deployment
│   │   ├── sync-to-aws.sh       # Sync changes to AWS
│   │   ├── cleanup-aws.sh       # Remove AWS resources
│   │   └── update-lambda.sh     # Update Lambda function
│   ├── 🧪 Testing Scripts
│   │   ├── test-deployment.sh   # Test AWS deployment
│   │   └── test-website.sh      # Test local website
│   ├── 🔧 Setup Scripts
│   │   ├── setup_snp_server.sh  # Setup S-parameter env
│   │   ├── setup_dalle_generator.sh # Setup image generation
│   │   ├── start_web_backend.sh # Start local backend
│   │   └── start_demo.sh        # Start demo environment
│   ├── 🔨 Fix Scripts
│   │   ├── fix-all-issues.sh    # Run all fixes
│   │   ├── fix-s21-*.sh         # S21-specific fixes
│   │   ├── fix-deployment.sh    # Deployment fixes
│   │   └── fix-*.sh             # Other specific fixes
│   └── 🔄 Utility Scripts
│       ├── clean-and-redeploy.sh # Clean and redeploy
│       ├── reorganize-project.sh # Project reorganization
│       └── use-exact-method.sh   # Switch S21 method
│
├── 📚 Documentation
│   ├── LOCAL_DEVELOPMENT_GUIDE.md # 🆕 Local dev setup
│   ├── AWS_DEPLOYMENT_GUIDE.md    # 🆕 AWS deployment
│   ├── PROJECT_STRUCTURE_GUIDE.md # 🆕 This file
│   ├── DEPLOYMENT_GUIDE.md        # 🔄 Updated deployment guide
│   ├── HOSTING_GUIDE.md           # Hosting options
│   ├── S21_Plotting_Solution.md   # S21 implementation
│   ├── SKRF_Solution_Guide.md     # scikit-rf usage
│   └── *.md                       # Other documentation
│
├── 📦 Dependencies & Config
│   ├── requirements.txt          # Python packages
│   ├── snp_requirements.txt      # S-parameter packages
│   └── *.json                    # Configuration files
│
├── 📁 Sample Data
│   ├── snpfiles/                 # S-parameter test files
│   │   ├── SfFilterSb.s2p       # Main demo file
│   │   └── *.s2p                # Other test files
│   └── videos/                   # Video assets
│
├── 🔄 Backward Compatibility
│   ├── deploy-aws.sh            # 🆕 Wrapper → scripts/
│   ├── sync-to-aws.sh           # 🆕 Wrapper → scripts/
│   ├── cleanup-aws.sh           # 🆕 Wrapper → scripts/
│   └── (other wrapper scripts)  # Redirect to scripts/
│
└── 🗂️ Legacy/Archive
    ├── organized-project/        # Previous reorganization attempt
    ├── backup_original_files/    # File backups
    ├── Website-Images/           # Old image structure
    ├── Website-Images2/          # Newer image structure
    └── __pycache__/             # Python cache
```

---

## 🔄 **Migration from Old Structure**

### **What Changed**
1. **All shell scripts moved** from root to `scripts/` directory
2. **New comprehensive documentation** added to `docs/`
3. **Backward compatibility wrappers** created in root
4. **Clear categorization** of all files by purpose

### **What Stayed the Same**
- All HTML files remain in root (for simple hosting)
- Python files remain in root (for easy imports)
- CSS and JS in their respective folders
- All functionality preserved

### **Backward Compatibility**
- Old script paths still work (redirect to new locations)
- All existing workflows continue to function
- Documentation updated to reference new paths

---

## 🎯 **Benefits of New Structure**

### **🧹 Cleaner Root Directory**
- No more 20+ shell scripts cluttering the root
- Clear separation of concerns
- Easier to find what you need

### **📚 Better Documentation**
- Comprehensive guides for local development and AWS deployment
- Clear script documentation with usage examples
- Structured documentation in `docs/` folder

### **🔧 Easier Maintenance**
- Scripts organized by category (deployment, testing, fixes)
- Clear naming conventions
- Centralized script documentation

### **🚀 Improved Developer Experience**
- New developers can quickly understand the project
- Clear setup instructions
- Comprehensive troubleshooting guides

---

## 🛠️ **Working with the New Structure**

### **Running Scripts**
```bash
# New way (recommended)
./scripts/deploy-aws.sh
./scripts/sync-to-aws.sh
./scripts/test-deployment.sh

# Old way (still works via wrappers)
./deploy-aws.sh
./sync-to-aws.sh
./test-deployment.sh
```

### **Finding Documentation**
```bash
# Local development
docs/LOCAL_DEVELOPMENT_GUIDE.md

# AWS deployment
docs/AWS_DEPLOYMENT_GUIDE.md

# Project structure
docs/PROJECT_STRUCTURE_GUIDE.md

# Script usage
scripts/README.md
```

### **Development Workflow**
```bash
# 1. Setup (first time)
./scripts/setup_snp_server.sh

# 2. Local development
./scripts/start_web_backend.sh

# 3. Test locally
./scripts/test-website.sh

# 4. Deploy to AWS
./scripts/sync-to-aws.sh

# 5. Test deployment
./scripts/test-deployment.sh
```

---

## 📋 **File Categories Explained**

### **Frontend Files (Root)**
- **Purpose**: Website content that users see
- **Location**: Root directory for simple hosting
- **Includes**: HTML, CSS, JS, images

### **Backend Files (Root)**
- **Purpose**: Server-side processing and APIs
- **Location**: Root for easy Python imports
- **Includes**: Lambda functions, S21 processors

### **Scripts Directory**
- **Purpose**: All automation and maintenance
- **Location**: `scripts/` for organization
- **Includes**: Deployment, testing, setup, fixes

### **Documentation Directory**
- **Purpose**: All project documentation
- **Location**: `docs/` for centralization
- **Includes**: Guides, references, troubleshooting

---

## 🔍 **Finding Specific Files**

### **Need to Deploy?**
- `scripts/deploy-aws.sh` - Full deployment
- `scripts/sync-to-aws.sh` - Update existing

### **Need to Setup Locally?**
- `docs/LOCAL_DEVELOPMENT_GUIDE.md` - Complete guide
- `scripts/setup_snp_server.sh` - Environment setup

### **Need to Fix Issues?**
- `scripts/fix-all-issues.sh` - Try all fixes
- `docs/ISSUE_ANALYSIS.md` - Common problems

### **Need to Understand S21?**
- `docs/S21_Plotting_Solution.md` - Implementation details
- `rf_process7.py` - Working local processor
- `lambda_no_numpy.py` - Working AWS processor

---

## 💡 **Best Practices**

### **For Developers**
1. **Read documentation first** - Check `docs/` before coding
2. **Use scripts for automation** - Don't run commands manually
3. **Test locally first** - Use local development guide
4. **Follow naming conventions** - Keep structure consistent

### **For Deployment**
1. **Use sync for updates** - `scripts/sync-to-aws.sh` for changes
2. **Use deploy for fresh start** - `scripts/deploy-aws.sh` for new setup
3. **Test after deployment** - `scripts/test-deployment.sh` always
4. **Keep backups** - Before major changes

### **For Maintenance**
1. **Update documentation** - When adding new features
2. **Categorize new scripts** - Put in appropriate `scripts/` category
3. **Maintain backward compatibility** - Add wrappers for breaking changes
4. **Regular cleanup** - Remove unused files periodically

---

## 🆘 **Need Help?**

- **Local Development**: `docs/LOCAL_DEVELOPMENT_GUIDE.md`
- **AWS Deployment**: `docs/AWS_DEPLOYMENT_GUIDE.md`
- **Script Usage**: `scripts/README.md`
- **Troubleshooting**: `docs/ISSUE_ANALYSIS.md`
- **S21 Issues**: `docs/S21_Plotting_Solution.md`
