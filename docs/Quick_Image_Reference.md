# Quick EDA Image Generation Reference

## Summary
You need to generate **20 EDA-related images** to replace the current generic website images. Each image should reflect your EDA business specializing in:
- **De-embedding** (Service 1)
- **RF Design** (Service 2) 
- **EM Simulation** (Service 3)

## Image List with Specific Prompts

### 🏠 HOME SECTION (8 images)

**Home-Hero.jpg & Home-Hero-Transparent.jpg**
*Main business hero image*
> "State-of-the-art EDA engineering facility, multiple workstations with advanced circuit design software, 3D electromagnetic simulations on large displays, professional engineers working, modern high-tech laboratory atmosphere"

**Home-About.jpg**
*Company overview*
> "Modern EDA company office, collaborative engineering team working on RF circuit designs, multiple monitors showing simulation results, professional workspace representing expertise"

**Home-Service-1.png** *(De-embedding)*
> "Vector network analyzer with S-parameter measurement setup, calibration standards, RF test fixtures, precision measurement visualization, clean technical illustration"

**Home-Service-2.png** *(RF Design)*
> "High-frequency PCB design layout, RF components and transmission lines, impedance matching networks, Smith chart, professional circuit design illustration"

**Home-Service-3.png** *(EM Simulation)*
> "3D electromagnetic field visualization with colorful field patterns, antenna radiation simulation, EM solver interface, vibrant technical illustration"

**Home-Testimonial.jpg**
*Background for testimonials*
> "Professional corporate office setting with subtle technology elements, clean atmosphere suitable for testimonial text overlay"

**Home/CTA.jpg**
*Call-to-action background*
> "Dynamic circuit patterns with electromagnetic waves, modern blue gradient, professional design suitable for overlay text"

### ℹ️ ABOUT SECTION (4 images)

**About-Hero.jpg & About-Hero-Transparent.jpg**
> "Professional EDA engineering workspace, modern electronics lab with oscilloscopes, spectrum analyzers, PCB design workstations, clean high-tech environment"

**About-Our-Mission.jpg**
> "Abstract EDA innovation concept, interconnected circuit patterns, electromagnetic wave visualization, blue-green corporate color scheme"

**About/CTA.jpg**
> "Subtle circuit board patterns background, electromagnetic field visualization, clean minimalist design for text overlay"

### 📞 CONTACT SECTION (2 images)

**Contact-Hero.jpg & Contact-Hero-Transparent.jpg**
> "Professional EDA consultation meeting room, large displays showing RF designs and EM simulations, welcoming corporate environment"

### 🔧 SERVICES SECTION (6 images)

**Services-Hero.jpg & Services-Hero-Transparent.jpg**
*Overview of all services*
> "Comprehensive EDA workspace showcasing de-embedding equipment, RF design stations, and EM simulation displays, integrated professional environment"

**Service-1.jpg** *(De-embedding detailed)*
> "Detailed RF measurement laboratory, vector network analyzer with multiple ports, precision calibration kit, S-parameter visualization"

**Service-2.jpg** *(RF Design detailed)*
> "Advanced PCB design software interface, high-frequency circuit layout, RF component placement, transmission line routing visualization"

**Service-3.jpg** *(EM Simulation detailed)*
> "Complex 3D electromagnetic simulation, antenna structure with radiation patterns, colorful field distribution, professional simulation workstation"

**Services/CTA.jpg**
> "Combined RF circuits, electromagnetic fields, and measurement equipment elements, professional gradient background"

## Generation Tips

### For AI Image Tools:
- **Style:** "Professional, photorealistic, modern, high-tech"
- **Colors:** Corporate blues, greens, clean whites
- **Quality:** "High resolution, professional photography style"
- **Avoid:** Generic stock photo looks, outdated equipment

### Technical Elements to Include:
- **De-embedding:** Vector network analyzers, S-parameters, calibration standards
- **RF Design:** PCB layouts, Smith charts, impedance matching, transmission lines
- **EM Simulation:** 3D field visualizations, antenna patterns, colorful EM fields

### File Naming Convention:
Save each generated image with the **exact filename** shown above in the corresponding Website-Images2 subfolder.

## Progress Tracking
Run `python3 create_image_structure.py` to check your progress and see which images are still needed.

## Next Steps After Generation:
1. Generate all 20 images using the prompts above
2. Save with exact filenames in Website-Images2 folders
3. Update your website code to reference Website-Images2 instead of Website-Images
4. Test the website with new EDA-themed images
