# 🎨 Automated EDA Image Generation with DALL-E

This automation system generates all 20 EDA-related images for your website using OpenAI's DALL-E API.

## 🚀 Quick Start

### 1. Setup
```bash
# Make setup script executable and run it
chmod +x setup_dalle_generator.sh
./setup_dalle_generator.sh
```

### 2. Get OpenAI API Key
1. Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Create a new API key
3. Set it as environment variable:
```bash
export OPENAI_API_KEY='your-api-key-here'
```

### 3. Generate Images
```bash
# Generate all images automatically
python3 dalle_image_generator.py

# Or use the interactive batch runner
python3 dalle_batch_runner.py
```

## 📁 What Gets Generated

The system creates **20 EDA-related images** organized in `Website-Images2/`:

```
Website-Images2/
├── About/
│   ├── About-Hero.jpg
│   ├── About-Hero-Transparent.jpg
│   ├── About-Our-Mission.jpg
│   └── CTA.jpg
├── Contact/
│   ├── Contact-Hero.jpg
│   └── Contact-Hero-Transparent.jpg
├── Home/
│   ├── CTA.jpg
│   ├── Home-About.jpg
│   ├── Home-Hero.jpg
│   ├── Home-Hero-Transparent.jpg
│   ├── Home-Service-1.png (De-embedding)
│   ├── Home-Service-2.png (RF Design)
│   ├── Home-Service-3.png (EM Simulation)
│   └── Home-Testimonial.jpg
└── Services/
    ├── CTA.jpg
    ├── Service-1.jpg (De-embedding)
    ├── Service-2.jpg (RF Design)
    ├── Service-3.jpg (EM Simulation)
    ├── Services-Hero.jpg
    └── Services-Hero-Transparent.jpg
```

## 🎯 Image Specifications

Each image is tailored for your EDA business:

### Service-Specific Images:
- **Service 1 (De-embedding)**: Vector network analyzers, S-parameters, calibration equipment
- **Service 2 (RF Design)**: PCB layouts, Smith charts, impedance matching
- **Service 3 (EM Simulation)**: 3D electromagnetic fields, antenna patterns, colorful visualizations

### Image Sizes:
- **Hero images**: 1792x1024 (wide format)
- **Service icons**: 1024x1024 (square format)
- **Other images**: 1024x1024 (square format)

## 🔧 Usage Options

### Full Automation
```bash
python3 dalle_image_generator.py
```
- Generates all 20 images
- 3-second delay between API calls
- Skips existing images
- Shows progress and summary

### Interactive Batch Runner
```bash
python3 dalle_batch_runner.py
```
Options:
1. Generate ALL images (20 total)
2. Generate only HOME section (8 images)
3. Generate only SERVICE icons (3 images)
4. Generate only HERO images (6 images)
5. Retry failed/missing images only
6. Generate specific image by name

### Progress Tracking
```bash
python3 create_image_structure.py
```
Shows which images are complete and which are still needed.

## 💰 Cost Estimation

**DALL-E 3 Pricing** (as of 2024):
- Standard quality: $0.040 per image
- **Total cost for 20 images: ~$0.80**

The script uses standard quality to keep costs reasonable while maintaining professional appearance.

## 🛠️ Troubleshooting

### API Key Issues
```bash
# Check if key is set
echo $OPENAI_API_KEY

# Set temporarily
export OPENAI_API_KEY='your-key-here'

# Set permanently (add to ~/.bashrc or ~/.zshrc)
echo 'export OPENAI_API_KEY="your-key-here"' >> ~/.bashrc
```

### Rate Limiting
- Script includes 3-second delays between requests
- If you hit rate limits, increase delay in the script
- Failed images can be retried by running the script again

### Image Quality
- All images use DALL-E 3 with "natural" style
- Prompts are optimized for professional EDA content
- If an image doesn't meet expectations, delete it and run the script again

## 📝 Customization

### Modify Prompts
Edit `dalle_image_generator.py` and update the `_define_images()` method to customize prompts.

### Change Image Sizes
Modify the `size` parameter in image definitions. DALL-E 3 supports:
- `1024x1024` (square)
- `1792x1024` (landscape)
- `1024x1792` (portrait)

### Add New Images
Add entries to the `image_definitions` dictionary in the format:
```python
"folder/filename.jpg": {
    "prompt": "Your detailed prompt here",
    "size": "1024x1024",
    "style": "natural"
}
```

## 🔄 After Generation

1. **Check Results**: Review generated images in `Website-Images2/`
2. **Update Website**: Modify your HTML/CSS to use `Website-Images2/` instead of `Website-Images/`
3. **Test Website**: Ensure all images load correctly
4. **Backup**: Keep the original `Website-Images/` as backup

## 📞 Support

If you encounter issues:
1. Check the console output for specific error messages
2. Verify your OpenAI API key is valid and has credits
3. Ensure you have internet connectivity
4. Try running the batch runner for selective generation

The automation handles most edge cases and provides detailed feedback on success/failure for each image.
