# 🎨 Services-Hero2.jpg Generation Guide

## 🎯 Overview
Generate a new EDA hero image `Services-Hero2.jpg` with a custom prompt and integrate it into your website.

## 📋 Step-by-Step Instructions

### **Option 1: Quick Generation (Recommended)**

#### Step 1: Set Your API Key
```bash
export OPENAI_API_KEY='your-api-key-here'
```

#### Step 2: Run Quick Generator
```bash
cd /home/<USER>/Documents/website
source dalle_env/bin/activate
python3 quick_generate_hero2.py
```

#### Step 3: Enter Your Custom Prompt
Replace "xxxx" with your desired prompt, for example:
- `"Advanced EDA simulation laboratory with electromagnetic field visualizations"`
- `"High-tech RF testing facility with vector network analyzers"`
- `"Modern electronics design workspace with circuit simulation software"`

### **Option 2: Full Integration Workflow**

#### Step 1: Run Full Generator
```bash
python3 generate_services_hero2.py
```

#### Step 2: Select Prompt
Choose from 5 suggested EDA prompts or enter custom prompt

#### Step 3: Choose Integration
1. **Replace current Services hero** - Updates services.html
2. **Use for pricing page** - Updates pricing.html  
3. **Create new section** - Manual integration
4. **A/B testing** - Keep both images

## 🎨 **Suggested Prompts for "xxxx"**

### **Technical Focus:**
- `"Advanced EDA simulation laboratory with electromagnetic field analysis workstations"`
- `"High-frequency RF measurement facility with vector network analyzers and test equipment"`
- `"Modern PCB design center with multi-layer circuit layout workstations"`

### **Professional Environment:**
- `"State-of-the-art electronics engineering facility with collaborative workspaces"`
- `"Professional EDA development center with advanced simulation software"`
- `"Modern electromagnetic compatibility testing laboratory"`

### **Simulation Focus:**
- `"3D electromagnetic field simulation center with colorful field visualizations"`
- `"Advanced antenna design laboratory with radiation pattern displays"`
- `"High-tech EM simulation facility with multiple analysis workstations"`

### **Equipment Focus:**
- `"Precision RF calibration laboratory with measurement standards"`
- `"Advanced signal integrity testing facility with high-speed equipment"`
- `"Professional microwave engineering laboratory with specialized instruments"`

## 🔧 **Manual Integration Steps**

If you want to manually integrate the new image:

### **Replace Services Page Hero:**
1. Edit `css/style.css`
2. Find line ~375: `.main_services`
3. Change: `Services-Hero.jpg` → `Services-Hero2.jpg`

### **Update Pricing Page:**
1. Edit `css/style.css`
2. Find line ~1715: `.main_pricing`
3. Change: `Services-Hero.jpg` → `Services-Hero2.jpg`

### **Add to New Section:**
```css
.new-section {
  background: url("../Website-Images2/Services/Services-Hero2.jpg") center / cover no-repeat;
  position: relative;
}

.new-section::before {
  content: '';
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: linear-gradient(135deg, rgba(0,0,0,0.6), rgba(48,58,77,0.4));
  z-index: 1;
}

.new-section .container {
  position: relative;
  z-index: 2;
}
```

## 📁 **File Locations**

### **Generated Image:**
```
Website-Images2/Services/Services-Hero2.jpg
```

### **Files Modified:**
- `dalle_image_generator.py` - Added new image definition
- `css/style.css` - Updated background image references (if using integration)

### **Generated Scripts:**
- `generate_services_hero2.py` - Full workflow with integration options
- `quick_generate_hero2.py` - Quick generation only

## 🎯 **Integration Options Explained**

### **Option 1: Replace Services Hero**
- **What**: Updates services.html to use new image
- **Effect**: Services page gets new hero image
- **Files changed**: `css/style.css`

### **Option 2: Update Pricing Page**
- **What**: Updates pricing.html to use new image  
- **Effect**: Pricing page gets new hero image
- **Files changed**: `css/style.css`

### **Option 3: New Section**
- **What**: Manual integration for new page section
- **Effect**: You add new section with new image
- **Files changed**: Manual HTML/CSS editing

### **Option 4: A/B Testing**
- **What**: Keep both images for testing
- **Effect**: You can switch between images manually
- **Files changed**: None (manual switching)

## 🎨 **Text Readability**

The new image will automatically have proper text readability features:
- ✅ **Dark gradient overlay** for contrast
- ✅ **White text with shadows** for visibility
- ✅ **Semi-transparent backgrounds** for text blocks
- ✅ **Backdrop blur effects** for modern appearance

## 💰 **Cost & Specifications**

- **Cost**: ~$0.04 (DALL-E 3 standard quality)
- **Size**: 1792x1024 (hero image format)
- **Format**: JPG
- **Quality**: Standard (optimized for web)

## 🚀 **Quick Commands Summary**

```bash
# Set API key
export OPENAI_API_KEY='your-key-here'

# Activate environment
source dalle_env/bin/activate

# Quick generation
python3 quick_generate_hero2.py

# Full workflow with integration
python3 generate_services_hero2.py

# Check if image was created
ls -la Website-Images2/Services/Services-Hero2.jpg
```

## 🎉 **Expected Result**

After generation, you'll have:
1. **New EDA hero image** with your custom prompt
2. **Proper file location** in Website-Images2/Services/
3. **Integration options** to use the image on your website
4. **Consistent styling** with text readability features

The new image will perfectly match your EDA business theme and integrate seamlessly with your existing website design!
