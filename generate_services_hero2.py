#!/usr/bin/env python3
"""
Generate Services-Hero2.jpg and provide integration options
"""

import os
from dalle_image_generator import EDAImageGenerator

def get_custom_prompt():
    """Get custom prompt from user"""
    print("🎨 Custom Prompt for Services-Hero2.jpg")
    print("-" * 40)
    print("Current placeholder prompt: 'xxxx'")
    print("\nSuggested EDA prompts:")
    print("1. Advanced EDA simulation laboratory with multiple workstations")
    print("2. High-tech electronics design facility with RF measurement equipment")
    print("3. Modern electromagnetic simulation center with 3D field displays")
    print("4. Professional EDA engineering workspace with circuit design software")
    print("5. State-of-the-art RF testing laboratory with vector network analyzers")
    print("6. Custom prompt (enter your own)")
    
    choice = input("\nSelect option (1-6) or press Enter for custom: ").strip()
    
    prompts = {
        "1": "Advanced EDA simulation laboratory with multiple workstations, electromagnetic field visualizations, professional engineers working, modern high-tech environment",
        "2": "High-tech electronics design facility with RF measurement equipment, vector network analyzers, circuit design workstations, professional laboratory setting",
        "3": "Modern electromagnetic simulation center with 3D field displays, colorful EM visualizations, antenna patterns, professional simulation workstations",
        "4": "Professional EDA engineering workspace with circuit design software, multiple monitors showing PCB layouts, collaborative engineering environment",
        "5": "State-of-the-art RF testing laboratory with vector network analyzers, S-parameter measurements, precision calibration equipment, professional setup"
    }
    
    if choice in prompts:
        selected_prompt = prompts[choice]
        print(f"\nSelected prompt: {selected_prompt}")
        confirm = input("Use this prompt? (y/n): ").lower().strip()
        if confirm == 'y':
            return selected_prompt
    
    # Custom prompt
    custom_prompt = input("\nEnter your custom prompt: ").strip()
    if not custom_prompt:
        return "Advanced EDA engineering laboratory with modern equipment and professional atmosphere"
    
    return custom_prompt

def update_image_definition(new_prompt):
    """Update the image definition with the new prompt"""
    print(f"\n🔧 Updating image definition with new prompt...")
    
    # Read the current file
    with open('dalle_image_generator.py', 'r') as f:
        content = f.read()
    
    # Replace the placeholder prompt
    updated_content = content.replace('"prompt": "xxxx"', f'"prompt": "{new_prompt}"')
    
    # Write back the updated content
    with open('dalle_image_generator.py', 'w') as f:
        f.write(updated_content)
    
    print("✅ Image definition updated!")

def generate_image(prompt):
    """Generate the Services-Hero2.jpg image"""
    print(f"\n🎨 Generating Services-Hero2.jpg...")
    print("-" * 35)
    
    # Check API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OpenAI API key not found!")
        print("Please set: export OPENAI_API_KEY='your-key-here'")
        return False
    
    # Initialize generator
    generator = EDAImageGenerator(api_key)
    
    # Generate the specific image
    image_path = "Services/Services-Hero2.jpg"
    config = {
        "prompt": prompt,
        "size": "1792x1024",
        "style": "natural"
    }
    
    print(f"📝 Prompt: {prompt}")
    print(f"📏 Size: 1792x1024 (hero image format)")
    print(f"💰 Cost: ~$0.04")
    
    proceed = input(f"\nProceed with generation? (y/n): ").lower().strip()
    if proceed != 'y':
        print("👋 Generation cancelled!")
        return False
    
    success = generator.generate_single_image(image_path, config)
    
    if success:
        print(f"✅ Services-Hero2.jpg generated successfully!")
        print(f"📁 Saved to: Website-Images2/Services/Services-Hero2.jpg")
        return True
    else:
        print(f"❌ Generation failed!")
        return False

def show_integration_options():
    """Show options for where to use the new image"""
    print(f"\n🔧 Integration Options for Services-Hero2.jpg")
    print("=" * 50)
    print("You can use this new image in several ways:")
    print()
    print("1. 🔄 Replace current Services hero image")
    print("   - Update services.html to use Services-Hero2.jpg")
    print("   - Replace the existing Services-Hero.jpg")
    print()
    print("2. 🆕 Add as alternative hero for pricing page")
    print("   - Update pricing.html to use Services-Hero2.jpg")
    print("   - Keep existing Services-Hero.jpg for services page")
    print()
    print("3. 🎯 Create new page section")
    print("   - Add new section to any page using Services-Hero2.jpg")
    print("   - Keep both existing images")
    print()
    print("4. 🔀 A/B test different hero images")
    print("   - Manually switch between images for testing")
    print()
    
    choice = input("Select integration option (1-4): ").strip()
    return choice

def apply_integration(choice):
    """Apply the selected integration option"""
    
    if choice == "1":
        # Replace current services hero
        print(f"\n🔄 Replacing Services hero image...")
        
        # Update CSS
        with open('css/style.css', 'r') as f:
            css_content = f.read()
        
        updated_css = css_content.replace(
            'url("../Website-Images2/Services/Services-Hero.jpg")',
            'url("../Website-Images2/Services/Services-Hero2.jpg")'
        )
        
        with open('css/style.css', 'w') as f:
            f.write(updated_css)
        
        print("✅ Services page now uses Services-Hero2.jpg")
        
    elif choice == "2":
        # Update pricing page
        print(f"\n🆕 Updating pricing page to use Services-Hero2.jpg...")
        
        with open('css/style.css', 'r') as f:
            css_content = f.read()
        
        updated_css = css_content.replace(
            '.main_pricing {\n  background: url("../Website-Images2/Services/Services-Hero.jpg")',
            '.main_pricing {\n  background: url("../Website-Images2/Services/Services-Hero2.jpg")'
        )
        
        with open('css/style.css', 'w') as f:
            f.write(updated_css)
        
        print("✅ Pricing page now uses Services-Hero2.jpg")
        
    elif choice == "3":
        print(f"\n🎯 New section option selected")
        print("You can manually add Services-Hero2.jpg to any page by:")
        print("1. Adding a new section in HTML")
        print("2. Creating CSS with: background: url('../Website-Images2/Services/Services-Hero2.jpg')")
        print("3. Adding the same overlay effects for text readability")
        
    elif choice == "4":
        print(f"\n🔀 A/B testing option selected")
        print("Both images are now available:")
        print("- Services-Hero.jpg (original)")
        print("- Services-Hero2.jpg (new)")
        print("You can manually switch between them in the CSS file")
        
    else:
        print(f"\n💾 Image generated and saved")
        print("You can manually integrate it later")

def main():
    """Main function"""
    print("🎨 Services-Hero2.jpg Generator")
    print("=" * 40)
    
    # Get custom prompt
    prompt = get_custom_prompt()
    
    # Update the image definition
    update_image_definition(prompt)
    
    # Generate the image
    success = generate_image(prompt)
    
    if success:
        # Show integration options
        choice = show_integration_options()
        
        # Apply integration
        apply_integration(choice)
        
        print(f"\n🎉 Process Complete!")
        print("=" * 25)
        print("✅ Services-Hero2.jpg generated")
        print("✅ Integration applied")
        print("🌐 Test your website to see the new image")
        
    else:
        print(f"\n❌ Generation failed. Please check your API key and try again.")

if __name__ == "__main__":
    main()
