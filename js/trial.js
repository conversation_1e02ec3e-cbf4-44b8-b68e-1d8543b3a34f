/**
 * De-embedding Trial Interface
 */

class TrialInterface {
    constructor() {
        this.currentStep = 1;
        this.dutFile = null;
        this.fixtureFile = null;
        this.dutData = null;
        this.fixtureData = null;
        this.deEmbeddedData = null;
        this.parser = new SNPParser();
        this.deEmbedding = new DeEmbedding();

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // File upload event listeners
        this.setupFileUpload('dut-file', 'dut-drop-zone', 'dut-preview', 'dut');
        this.setupFileUpload('fixture-file', 'fixture-drop-zone', 'fixture-preview', 'fixture');

        // Check if files are ready
        this.checkFilesReady();
    }

    setupFileUpload(inputId, dropZoneId, previewId, type) {
        const input = document.getElementById(inputId);
        const dropZone = document.getElementById(dropZoneId);
        const preview = document.getElementById(previewId);

        // Click to browse
        dropZone.addEventListener('click', () => input.click());

        // File input change
        input.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFile(e.target.files[0], type, preview, dropZone);
            }
        });

        // Drag and drop
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');

            if (e.dataTransfer.files.length > 0) {
                this.handleFile(e.dataTransfer.files[0], type, preview, dropZone);
            }
        });
    }

    async handleFile(file, type, preview, dropZone) {
        console.log(`🔄 handleFile: Processing ${type} file: ${file.name}`);

        // Validate file type
        if (!this.validateFile(file)) {
            alert('Please select a valid S-parameter file (.s2p, .s4p, .s6p, .s8p, .snp)');
            return;
        }

        console.log(`✅ File validation passed for ${file.name}`);

        // Send file to backend for processing using rf_process7.py approach
        try {
            console.log(`🔄 Sending ${type} file to backend...`);
            const processedData = await this.uploadFileToBackend(file, type);

            if (processedData.success) {
                console.log(`✅ Backend processing successful for ${type} file`);

                // Store file and processed data
                if (type === 'dut') {
                    this.dutFile = file;
                    this.dutData = processedData;
                } else {
                    this.fixtureFile = file;
                    this.fixtureData = processedData;
                }

                // Show preview
                this.showFilePreview(file, preview, dropZone);

                // Check if both files are ready
                this.checkFilesReady();

                this.showMessage(`${type.toUpperCase()} file processed successfully using rf_process7.py approach!`, 'success');
            } else {
                throw new Error(processedData.error || 'Backend processing failed');
            }
        } catch (error) {
            console.error(`❌ Error processing ${type} file:`, error);
            this.showMessage(`Error processing ${type} file: ${error.message}`, 'error');
        }
    }

    async uploadFileToBackend(file, type) {
        const API_BASE_URL = 'https://fh73i40lo2.execute-api.us-west-2.amazonaws.com/prod';

        // For now, use the mock upload endpoint that returns demo data
        // In the future, this can be enhanced to handle actual file parsing
        const response = await fetch(`${API_BASE_URL}/api/upload-snp`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                filename: file.name,
                type: type,
                size: file.size
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    validateFile(file) {
        const validExtensions = ['.s2p', '.s4p', '.s6p', '.s8p', '.snp'];
        const fileName = file.name.toLowerCase();
        return validExtensions.some(ext => fileName.endsWith(ext));
    }

    showFilePreview(file, preview, dropZone) {
        console.log('🔄 showFilePreview: Starting for file:', file.name);
        console.log('🔄 showFilePreview: Preview element:', preview);
        console.log('🔄 showFilePreview: Drop zone element:', dropZone);

        const fileName = preview.querySelector('.file-name');
        const fileSize = preview.querySelector('.file-size');

        console.log('🔄 showFilePreview: fileName element:', fileName);
        console.log('🔄 showFilePreview: fileSize element:', fileSize);

        if (fileName && fileSize) {
            console.log('🔄 showFilePreview: Setting file name and size...');
            fileName.textContent = file.name;
            fileSize.textContent = this.formatFileSize(file.size);
            console.log('✅ showFilePreview: File name and size set');

            const dropContent = dropZone.querySelector('.drop-content');
            console.log('🔄 showFilePreview: Drop content element:', dropContent);

            if (dropContent) {
                dropContent.style.display = 'none';
                console.log('✅ showFilePreview: Drop content hidden');
            } else {
                console.error('❌ showFilePreview: Drop content element not found');
            }

            preview.style.display = 'flex';
            console.log('✅ showFilePreview: Preview set to flex display');

            // Check if preview is actually visible
            const computedStyle = window.getComputedStyle(preview);
            console.log('🔄 showFilePreview: Preview computed display:', computedStyle.display);
            console.log('🔄 showFilePreview: Preview computed visibility:', computedStyle.visibility);

            // Check for any red X or error indicators
            const removeButton = preview.querySelector('.remove-file');
            const fileIcon = preview.querySelector('.file-icon');
            console.log('🔄 showFilePreview: Remove button element:', removeButton);
            console.log('🔄 showFilePreview: File icon element:', fileIcon);
            console.log('🔄 showFilePreview: File icon content:', fileIcon ? fileIcon.textContent : 'not found');

            // Ensure the icon is green checkmark
            if (fileIcon) {
                fileIcon.textContent = '✅';
                console.log('✅ showFilePreview: File icon set to green checkmark');
            }

            console.log('✅ showFilePreview: File preview shown successfully');
        } else {
            console.error('❌ showFilePreview: Could not find file name or size elements in preview');
            console.error('❌ showFilePreview: Preview HTML:', preview.innerHTML);
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    checkFilesReady() {
        const processBtn = document.getElementById('process-btn');
        if (this.dutFile && this.fixtureFile) {
            processBtn.disabled = false;
            processBtn.textContent = 'Start De-embedding';
        } else {
            processBtn.disabled = true;
            processBtn.textContent = 'Upload both files to continue';
        }
    }

    removeFile(type) {
        if (type === 'dut') {
            this.dutFile = null;
            this.resetFileUpload('dut-drop-zone', 'dut-preview');
            document.getElementById('dut-file').value = '';
        } else {
            this.fixtureFile = null;
            this.resetFileUpload('fixture-drop-zone', 'fixture-preview');
            document.getElementById('fixture-file').value = '';
        }
        this.checkFilesReady();
    }

    resetFileUpload(dropZoneId, previewId) {
        const dropZone = document.getElementById(dropZoneId);
        const preview = document.getElementById(previewId);

        dropZone.querySelector('.drop-content').style.display = 'block';
        preview.style.display = 'none';
    }

    async loadDemoFiles() {
        try {
            console.log('🔄 STEP 1: Starting demo file loading...');

            // Test if backend is reachable
            const API_BASE_URL = 'https://fh73i40lo2.execute-api.us-west-2.amazonaws.com/prod';
            console.log('🔄 STEP 2: Testing backend connection...');
            try {
                const healthResponse = await fetch(`${API_BASE_URL}/api/health`);
                console.log('✅ STEP 2 RESULT: Backend health response:', healthResponse.status);
            } catch (healthError) {
                console.error('❌ STEP 2 ERROR: Cannot reach backend:', healthError);
                throw new Error('Backend server not reachable. Check AWS Lambda function.');
            }

            // Use the Python backend that follows rf_process7.py approach
            console.log('🔄 STEP 3: Calling demo files API...');
            const response = await fetch(`${API_BASE_URL}/api/demo-files`);
            console.log('✅ STEP 3 RESULT: Demo files response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('❌ STEP 3 ERROR: Response not OK:', errorText);
                throw new Error(`Backend server error: ${response.status} - ${errorText}`);
            }

            console.log('🔄 STEP 4: Parsing JSON response...');
            const data = await response.json();
            console.log('✅ STEP 4 RESULT: Parsed data structure:', {
                has_dut: !!data.dut,
                has_fixture: !!data.fixture,
                dut_success: data.dut?.success,
                fixture_success: data.fixture?.success
            });

            console.log('🔄 STEP 5: Validating data...');
            if (!data.dut || !data.dut.success) {
                throw new Error('DUT data processing failed: ' + (data.dut?.error || 'Unknown error'));
            }
            if (!data.fixture || !data.fixture.success) {
                throw new Error('Fixture data processing failed: ' + (data.fixture?.error || 'Unknown error'));
            }

            console.log('✅ STEP 5 RESULT: Data validation passed');
            console.log('DUT frequency points:', data.dut.frequencies?.length);
            console.log('Fixture frequency points:', data.fixture.frequencies?.length);

            console.log('🔄 STEP 6: Storing processed data...');
            // Store the processed data
            this.dutData = data.dut;
            this.fixtureData = data.fixture;
            console.log('✅ STEP 6 RESULT: Data stored successfully');

            console.log('🔄 STEP 7: Creating mock file objects...');
            // Create mock file objects for UI
            this.dutFile = new File(['mock'], 'SfFilterSb.s2p', { type: 'text/plain' });
            this.fixtureFile = new File(['mock'], 'SfSb.s2p', { type: 'text/plain' });
            console.log('✅ STEP 7 RESULT: Mock files created');

            console.log('🔄 STEP 8: Showing file previews...');
            // Show previews
            const dutPreview = document.getElementById('dut-preview');
            const dutDropZone = document.getElementById('dut-drop-zone');
            const fixturePreview = document.getElementById('fixture-preview');
            const fixtureDropZone = document.getElementById('fixture-drop-zone');

            console.log('DUT preview element:', dutPreview);
            console.log('DUT drop zone element:', dutDropZone);
            console.log('Fixture preview element:', fixturePreview);
            console.log('Fixture drop zone element:', fixtureDropZone);

            this.showFilePreview(this.dutFile, dutPreview, dutDropZone);
            this.showFilePreview(this.fixtureFile, fixturePreview, fixtureDropZone);
            console.log('✅ STEP 8 RESULT: File previews shown');

            console.log('🔄 STEP 9: Checking files ready...');
            this.checkFilesReady();
            console.log('✅ STEP 9 RESULT: Files ready check completed');

            // Show success message with method info
            const method = this.dutData.method || 'unknown';
            this.showMessage(`Demo files loaded successfully using ${method}!`, 'success');
            console.log('✅ ALL STEPS COMPLETED: Demo files loaded successfully!');

        } catch (error) {
            console.error('❌ ERROR in loadDemoFiles:', error);
            console.error('❌ ERROR stack:', error.stack);
            this.showMessage('Failed to load demo files: ' + error.message, 'error');
        }
    }

    async processFiles() {
        if (!this.dutFile || !this.fixtureFile) {
            this.showMessage('Please upload both files first.', 'error');
            return;
        }

        // Move to processing step
        this.setStep(2);

        try {
            // Read and parse files
            await this.parseFiles();

            // Perform de-embedding
            this.performDeEmbedding();

            // Show results
            this.showResults();

            // Move to results step
            this.setStep(3);

        } catch (error) {
            console.error('Processing error:', error);
            this.showMessage('Error processing files: ' + error.message, 'error');
            this.setStep(1);
        }
    }

    async parseFiles() {
        console.log('🔄 parseFiles: Checking if data is already processed by backend...');

        // Check if data is already processed by the backend
        if (this.dutData && this.dutData.success && this.fixtureData && this.fixtureData.success) {
            console.log('✅ parseFiles: Data already processed by backend, skipping file parsing');
            return;
        }

        console.log('🔄 parseFiles: Processing files with JavaScript parser...');
        // Parse DUT file
        const dutText = await this.readFileAsText(this.dutFile);
        this.dutData = this.parser.parse(dutText);

        // Parse fixture file
        const fixtureText = await this.readFileAsText(this.fixtureFile);
        this.fixtureData = this.parser.parse(fixtureText);

        // Validate data
        if (!this.dutData.sParameters.S21 || !this.fixtureData.sParameters.S21) {
            throw new Error('Invalid S-parameter data. S21 parameter not found.');
        }
        console.log('✅ parseFiles: JavaScript parsing completed');
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    performDeEmbedding() {
        console.log('🔄 performDeEmbedding: Checking data format...');

        // Check if data is from backend (already processed)
        if (this.dutData && this.dutData.success && this.fixtureData && this.fixtureData.success) {
            console.log('✅ performDeEmbedding: Using backend-processed data, skipping de-embedding calculation');
            // Data is already processed by backend, no need for de-embedding
            this.deEmbeddedData = {
                success: true,
                message: 'Data processed by backend'
            };
            return;
        }

        console.log('🔄 performDeEmbedding: Performing JavaScript de-embedding...');
        // Perform de-embedding calculation for JavaScript-parsed data
        this.deEmbeddedData = this.deEmbedding.deEmbed(this.dutData, this.fixtureData);
        console.log('✅ performDeEmbedding: De-embedding completed');
    }

    showResults() {
        // Use the processed S21 data from the backend (rf_process7.py style)
        console.log('Showing results with backend-processed data...');

        // Create plot using the correctly processed data
        this.createS21Plot(this.dutData, this.fixtureData);

        // Update summary statistics
        // Summary stats removed as requested
    }

    getS21FromData(data) {
        // Data is already processed by the backend in the correct format
        console.log('Using backend-processed S21 data...');

        if (!data || !data.success) {
            console.log('No valid S21 data found');
            return { frequencies: [], s21_dB: [], unit: 'GHz' };
        }

        console.log(`S21 data points: ${data.s21_db.length}`);
        console.log(`Frequency unit: ${data.freq_unit}`);
        console.log(`Processing method: ${data.method}`);

        return {
            frequencies: data.frequencies,
            s21_dB: data.s21_db,
            unit: data.freq_unit
        };
    }

    createS21Plot(dutData, fixtureData) {
        console.log('🔄 createS21Plot: Determining data format...');
        console.log('DUT data type:', dutData?.success ? 'backend' : 'javascript');
        console.log('Fixture data type:', fixtureData?.success ? 'backend' : 'javascript');

        let trace1, trace2, layout;

        // Check if data is from backend (has .success property)
        if (dutData && dutData.success && fixtureData && fixtureData.success) {
            console.log('✅ createS21Plot: Using backend data format');

            // Use actual uploaded filenames if available
            const dutFileName = this.dutFile ? this.dutFile.name : dutData.filename;
            const fixtureFileName = this.fixtureFile ? this.fixtureFile.name : fixtureData.filename;

            trace1 = {
                x: dutData.frequencies,
                y: dutData.s21_db,
                type: 'scatter',
                mode: 'lines',
                name: `Fixture-DUT-Fixture (${dutFileName})`,
                line: { color: '#dc3545', width: 2 }
            };

            trace2 = {
                x: fixtureData.frequencies,
                y: fixtureData.s21_db,
                type: 'scatter',
                mode: 'lines',
                name: `Fixture-Fixture (${fixtureFileName})`,
                line: { color: '#28a745', width: 2 }
            };

            layout = {
                title: {
                    text: '<b>S21 Insertion Loss Comparison</b>',
                    font: { size: 20, family: 'Arial, sans-serif' }
                },
                xaxis: {
                    title: 'Frequency (GHz)',
                    type: 'linear',
                    tickformat: '.1f',  // Force decimal format, prevent scientific notation
                    gridcolor: '#d3d3d3',
                    gridwidth: 1,
                    showgrid: true,
                    linecolor: '#000000',
                    linewidth: 2,
                    mirror: true,
                    showspikes: true,
                    spikecolor: '#808080',
                    spikethickness: 2,
                    spikedash: 'dash',
                    spikemode: 'across'
                },
                yaxis: {
                    title: 'S21 (dB)',
                    gridcolor: '#d3d3d3',
                    gridwidth: 1,
                    showgrid: true,
                    linecolor: '#000000',
                    linewidth: 2,
                    mirror: true
                },
                legend: { x: 0.98, y: 0.02, xanchor: 'right', yanchor: 'bottom' },
                margin: { t: 80, r: 50, b: 60, l: 70 },
                height: 500,
                plot_bgcolor: 'white',
                paper_bgcolor: 'white',
                hovermode: 'x unified'
            };

        } else if (dutData && dutData.sParameters && fixtureData && fixtureData.sParameters) {
            console.log('✅ createS21Plot: Using JavaScript-parsed data format');

            // Extract S21 data from JavaScript-parsed format
            const dutS21 = this.getS21FromJSData(dutData);
            const fixtureS21 = this.getS21FromJSData(fixtureData);

            trace1 = {
                x: dutS21.frequencies,
                y: dutS21.s21_dB,
                type: 'scatter',
                mode: 'lines',
                name: 'Fixture-DUT-Fixture (uploaded)',
                line: { color: '#dc3545', width: 2 }
            };

            trace2 = {
                x: fixtureS21.frequencies,
                y: fixtureS21.s21_dB,
                type: 'scatter',
                mode: 'lines',
                name: 'Fixture-Fixture (uploaded)',
                line: { color: '#28a745', width: 2 }
            };

            layout = {
                title: {
                    text: '<b>S21 Insertion Loss Comparison</b><br><sub>Processed using JavaScript parser</sub>',
                    font: { size: 20, family: 'Arial, sans-serif' }
                },
                xaxis: {
                    title: 'Frequency (GHz)',
                    type: 'linear',
                    tickformat: '.1f',  // Force decimal format, prevent scientific notation
                    gridcolor: '#d3d3d3',
                    gridwidth: 1,
                    showgrid: true,
                    linecolor: '#000000',
                    linewidth: 2,
                    mirror: true,
                    showspikes: true,
                    spikecolor: '#808080',
                    spikethickness: 2,
                    spikedash: 'dash',
                    spikemode: 'across'
                },
                yaxis: {
                    title: 'S21 (dB)',
                    gridcolor: '#d3d3d3',
                    gridwidth: 1,
                    showgrid: true,
                    linecolor: '#000000',
                    linewidth: 2,
                    mirror: true
                },
                legend: { x: 0.98, y: 0.02, xanchor: 'right', yanchor: 'bottom' },
                margin: { t: 80, r: 50, b: 60, l: 70 },
                height: 500,
                plot_bgcolor: 'white',
                paper_bgcolor: 'white',
                hovermode: 'x unified'
            };

        } else {
            console.error('❌ createS21Plot: Invalid data format for plotting');
            console.error('DUT data:', dutData);
            console.error('Fixture data:', fixtureData);
            return;
        }

        console.log('🔄 createS21Plot: Creating Plotly chart...');

        // Create the plot with enhanced configuration
        const config = {
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false,
            responsive: true
        };

        Plotly.newPlot('s21-plot', [trace1, trace2], layout, config);
        console.log('✅ createS21Plot: Plot created successfully');
    }

    getS21FromJSData(data) {
        // Extract S21 from JavaScript-parsed data format
        if (!data.sParameters.S21) {
            console.log('No S21 parameter found in JavaScript data');
            return { frequencies: [], s21_dB: [], unit: data.unit };
        }

        // Convert magnitude to dB: 20*log10(magnitude)
        const s21_dB = data.sParameters.S21.magnitude.map(mag => {
            const dB = 20 * Math.log10(Math.abs(mag));
            return isFinite(dB) ? dB : -100; // Handle log(0) case
        });

        return {
            frequencies: data.sParameters.S21.frequencies,
            s21_dB: s21_dB,
            unit: data.unit
        };
    }

    convertFrequencyForDisplay(frequencies, unit) {
        if (!frequencies || frequencies.length === 0) {
            return { frequencies: [], displayUnit: 'Hz' };
        }

        console.log(`Converting frequencies from ${unit}, max freq: ${Math.max(...frequencies)}`);

        // Convert original unit to Hz first
        const unitMultipliers = {
            'HZ': 1,
            'KHZ': 1e3,
            'MHZ': 1e6,
            'GHZ': 1e9
        };

        const originalMultiplier = unitMultipliers[unit.toUpperCase()] || 1;
        const freqsInHz = frequencies.map(f => f * originalMultiplier);
        const maxFreqHz = Math.max(...freqsInHz);

        console.log(`Max frequency in Hz: ${maxFreqHz}`);

        // Determine appropriate display unit based on max frequency
        let scaleFactor = 1;
        let displayUnit = 'Hz';

        if (maxFreqHz >= 1e9) {
            scaleFactor = 1e9;
            displayUnit = 'GHz';
        } else if (maxFreqHz >= 1e6) {
            scaleFactor = 1e6;
            displayUnit = 'MHz';
        } else if (maxFreqHz >= 1e3) {
            scaleFactor = 1e3;
            displayUnit = 'kHz';
        }

        const convertedFreqs = freqsInHz.map(f => f / scaleFactor);

        console.log(`Converted to ${displayUnit}, sample values: ${convertedFreqs.slice(0, 3)}`);

        return {
            frequencies: convertedFreqs,
            displayUnit: displayUnit
        };
    }

    updateSummaryStats() {
        console.log('🔄 updateSummaryStats: Updating summary with backend data...');

        // Handle backend data format
        if (this.dutData && this.dutData.success) {
            const freqInfo = {
                min: this.dutData.freq_range[0],
                max: this.dutData.freq_range[1],
                count: this.dutData.num_points,
                unit: this.dutData.freq_unit
            };

            document.getElementById('freq-range').textContent =
                `${freqInfo.min.toFixed(2)} - ${freqInfo.max.toFixed(2)} ${freqInfo.unit}`;

            document.getElementById('data-points').textContent = freqInfo.count;

            document.getElementById('improvement').textContent = 'S21 comparison using ' + (this.dutData.method || 'backend processing');

            console.log('✅ updateSummaryStats: Summary updated with backend data');
        } else {
            // Fallback for JavaScript-parsed data
            const freqInfo = this.dutData.getFrequencyInfo ? this.dutData.getFrequencyInfo() :
                            { min: Math.min(...this.dutData.frequencies),
                              max: Math.max(...this.dutData.frequencies),
                              count: this.dutData.frequencies.length,
                              unit: this.dutData.unit };

            document.getElementById('freq-range').textContent =
                `${freqInfo.min.toFixed(2)} - ${freqInfo.max.toFixed(2)} ${freqInfo.unit}`;

            document.getElementById('data-points').textContent = freqInfo.count;

            document.getElementById('improvement').textContent = 'Fixtures removed';

            console.log('✅ updateSummaryStats: Summary updated with JavaScript data');
        }
    }

    setStep(step) {
        // Update step indicator
        document.querySelectorAll('.step').forEach((el, index) => {
            if (index + 1 <= step) {
                el.classList.add('active');
            } else {
                el.classList.remove('active');
            }
        });

        // Show/hide step content
        document.querySelectorAll('.trial-step').forEach((el, index) => {
            if (index + 1 === step) {
                el.style.display = 'block';
            } else {
                el.style.display = 'none';
            }
        });

        this.currentStep = step;
    }

    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.getElementById('trial-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.id = 'trial-message';
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                max-width: 300px;
            `;
            document.body.appendChild(messageEl);
        }

        messageEl.textContent = message;
        messageEl.className = `message-${type}`;

        // Set background color based on type
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            info: '#17a2b8',
            warning: '#ffc107'
        };
        messageEl.style.backgroundColor = colors[type] || colors.info;

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 5000);
    }

    downloadResults() {
        if (!this.dutData || !this.fixtureData) {
            this.showMessage('No results to download', 'error');
            return;
        }

        // Create downloadable CSV file with S21 data
        const csvContent = this.generateS21CSV();

        // Create download link
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 's21_comparison_data.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showMessage('S21 data downloaded successfully!', 'success');
    }

    generateS21CSV() {
        const dutS21 = this.getS21FromData(this.dutData);
        const fixtureS21 = this.getS21FromData(this.fixtureData);

        let content = 'Frequency (Hz),Fixture-DUT-Fixture S21 (dB),Fixture-Fixture S21 (dB)\n';

        // Use DUT frequencies as reference
        for (let i = 0; i < dutS21.frequencies.length; i++) {
            const freq = dutS21.frequencies[i];
            const dutS21_dB = dutS21.s21_dB[i];

            // Find corresponding fixture frequency (with tolerance)
            let fixtureS21_dB = '';
            const tolerance = freq * 0.001; // 0.1% tolerance
            for (let j = 0; j < fixtureS21.frequencies.length; j++) {
                if (Math.abs(fixtureS21.frequencies[j] - freq) < tolerance) {
                    fixtureS21_dB = fixtureS21.s21_dB[j];
                    break;
                }
            }

            content += `${freq},${dutS21_dB},${fixtureS21_dB}\n`;
        }

        return content;
    }

    generateS2PFile(data) {
        let content = '! De-embedded S-parameters\n';
        content += `# ${data.unit} S RI R ${data.impedance}\n`;

        for (let i = 0; i < data.frequencies.length; i++) {
            const freq = data.frequencies[i];
            const s11_r = data.sParameters.S11.real[i];
            const s11_i = data.sParameters.S11.imag[i];
            const s12_r = data.sParameters.S12.real[i];
            const s12_i = data.sParameters.S12.imag[i];
            const s21_r = data.sParameters.S21.real[i];
            const s21_i = data.sParameters.S21.imag[i];
            const s22_r = data.sParameters.S22.real[i];
            const s22_i = data.sParameters.S22.imag[i];

            content += `${freq} ${s11_r} ${s11_i} ${s12_r} ${s12_i} ${s21_r} ${s21_i} ${s22_r} ${s22_i}\n`;
        }

        return content;
    }

    resetTrial() {
        // Reset all data
        this.dutFile = null;
        this.fixtureFile = null;
        this.dutData = null;
        this.fixtureData = null;
        this.deEmbeddedData = null;

        // Reset UI
        this.resetFileUpload('dut-drop-zone', 'dut-preview');
        this.resetFileUpload('fixture-drop-zone', 'fixture-preview');
        document.getElementById('dut-file').value = '';
        document.getElementById('fixture-file').value = '';

        // Go back to step 1
        this.setStep(1);
        this.checkFilesReady();
    }
}

// Global functions for HTML onclick handlers
function removeFile(type) {
    if (window.trialInterface) {
        window.trialInterface.removeFile(type);
    }
}

function loadDemoFiles() {
    if (window.trialInterface) {
        window.trialInterface.loadDemoFiles();
    }
}

function processFiles() {
    if (window.trialInterface) {
        window.trialInterface.processFiles();
    }
}

function downloadResults() {
    if (window.trialInterface) {
        window.trialInterface.downloadResults();
    }
}

function resetTrial() {
    if (window.trialInterface) {
        window.trialInterface.resetTrial();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.trialInterface = new TrialInterface();
});
