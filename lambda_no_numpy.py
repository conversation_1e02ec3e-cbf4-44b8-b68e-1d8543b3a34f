import json
import math

def lambda_handler(event, context):
    """
    Lambda function without numpy dependencies
    Uses pure Python to generate demo data matching plot_s21_simple.py
    """
    
    # Enable CORS
    headers = {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    }
    
    try:
        # Handle OPTIONS request (CORS preflight)
        if event.get('httpMethod') == 'OPTIONS':
            return {
                'statusCode': 200,
                'headers': headers,
                'body': ''
            }
        
        # Get the path
        path = event.get('path', '')
        method = event.get('httpMethod', 'GET')
        
        print(f"Processing: {method} {path}")
        
        # Health check endpoint
        if '/health' in path:
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({
                    'status': 'healthy', 
                    'service': 'EDA Backend Lambda',
                    'version': '10.0 - No numpy, pure Python',
                    'timestamp': context.aws_request_id
                })
            }
        
        # Demo files endpoint - pure Python implementation
        if '/demo-files' in path:
            print("Generating demo data using pure Python (no numpy)")
            
            # Generate 801 frequency points from 3.40 to 4.40 GHz
            # Using pure Python instead of numpy
            num_points = 801
            freq_start = 3.40
            freq_end = 4.40
            
            frequencies = []
            dut_s21_db = []
            fixture_s21_db = []
            
            for i in range(num_points):
                # Calculate frequency
                freq = freq_start + (freq_end - freq_start) * i / (num_points - 1)
                frequencies.append(freq)
                
                # Calculate progress (0 to 1)
                progress = i / (num_points - 1)
                
                # DUT S21: Filter behavior from -85.47 dB to -0.39 dB
                # Based on your plot_s21_simple.py output
                dut_base = -85.47 + (85.47 - 0.39) * progress
                
                # Add realistic filter ripple using pure Python math
                ripple = 3.0 * math.sin(progress * math.pi * 6) * math.exp(-progress * 1.5)
                dut_s21_value = dut_base + ripple
                
                # Ensure within bounds
                dut_s21_value = max(-89.64, min(-0.39, dut_s21_value))
                dut_s21_db.append(dut_s21_value)
                
                # Fixture S21: Excellent performance from -0.21 to -0.30 dB
                fixture_base = -0.21 - 0.09 * progress
                
                # Add small variations
                variation = 0.01 * math.sin(progress * math.pi * 15)
                fixture_s21_value = fixture_base + variation
                
                # Ensure within bounds
                fixture_s21_value = max(-0.30, min(-0.21, fixture_s21_value))
                fixture_s21_db.append(fixture_s21_value)
            
            print(f"Generated {len(frequencies)} frequency points")
            print(f"DUT S21 range: {min(dut_s21_db):.2f} to {max(dut_s21_db):.2f} dB")
            print(f"Fixture S21 range: {min(fixture_s21_db):.2f} to {max(fixture_s21_db):.2f} dB")
            print(f"DUT first point: {dut_s21_db[0]:.2f} dB")
            print(f"Fixture first point: {fixture_s21_db[0]:.2f} dB")
            
            # Create response matching your plot_s21_simple.py output
            response_data = {
                'dut': {
                    'frequencies': frequencies,
                    's21_db': dut_s21_db,
                    'filename': 'SfFilterSb.s2p',
                    'success': True,
                    'method': 'pure_python_no_numpy',
                    'freq_unit': 'GHz',
                    'data_points': len(frequencies),
                    'freq_range': f"{frequencies[0]:.2f} - {frequencies[-1]:.2f} GHz",
                    's21_range': f"{min(dut_s21_db):.2f} to {max(dut_s21_db):.2f} dB"
                },
                'fixture': {
                    'frequencies': frequencies,
                    's21_db': fixture_s21_db,
                    'filename': 'SfSb.s2p',
                    'success': True,
                    'method': 'pure_python_no_numpy',
                    'freq_unit': 'GHz',
                    'data_points': len(frequencies),
                    'freq_range': f"{frequencies[0]:.2f} - {frequencies[-1]:.2f} GHz",
                    's21_range': f"{min(fixture_s21_db):.2f} to {max(fixture_s21_db):.2f} dB"
                }
            }
            
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps(response_data)
            }
        
        # File upload endpoint - placeholder for real implementation
        if '/upload-snp' in path and method == 'POST':
            print("File upload endpoint - placeholder for real SNP processing")
            
            # TODO: Implement real SNP file processing here
            # Will need to add ntwk_1.py or equivalent S-parameter parsing
            
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({
                    'success': True,
                    'message': 'File upload received - real processing not yet implemented',
                    'method': 'placeholder_for_real_snp_processing',
                    'note': 'Need to implement ntwk_1.py equivalent in Lambda'
                })
            }
        
        # Default response
        return {
            'statusCode': 404,
            'headers': headers,
            'body': json.dumps({
                'error': 'Endpoint not found',
                'path': path,
                'method': method,
                'available_endpoints': ['/api/health', '/api/demo-files', '/api/upload-snp']
            })
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({
                'error': str(e),
                'type': 'lambda_error',
                'note': 'Check CloudWatch logs for details'
            })
        }
