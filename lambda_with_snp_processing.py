import json
import math
import re
import base64
import io

def lambda_handler(event, context):
    """
    Lambda function with real SNP file processing using rf_process7.py method
    """
    
    # Enable CORS
    headers = {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    }
    
    try:
        # Handle OPTIONS request (CORS preflight)
        if event.get('httpMethod') == 'OPTIONS':
            return {
                'statusCode': 200,
                'headers': headers,
                'body': ''
            }
        
        # Get the path
        path = event.get('path', '')
        method = event.get('httpMethod', 'GET')
        
        print(f"Processing: {method} {path}")
        
        # Health check endpoint
        if '/health' in path:
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({
                    'status': 'healthy', 
                    'service': 'EDA Backend Lambda',
                    'version': '11.0 - Real SNP processing using rf_process7.py method',
                    'timestamp': context.aws_request_id
                })
            }
        
        # Demo files endpoint - pure Python implementation
        if '/demo-files' in path:
            print("Generating demo data using pure Python (no numpy)")
            
            # Generate 801 frequency points from 3.40 to 4.40 GHz
            num_points = 801
            freq_start = 3.40
            freq_end = 4.40
            
            frequencies = []
            dut_s21_db = []
            fixture_s21_db = []
            
            for i in range(num_points):
                # Calculate frequency
                freq = freq_start + (freq_end - freq_start) * i / (num_points - 1)
                frequencies.append(freq)
                
                # Calculate progress (0 to 1)
                progress = i / (num_points - 1)
                
                # DUT S21: Filter behavior from -85.47 dB to -0.39 dB
                dut_base = -85.47 + (85.47 - 0.39) * progress
                ripple = 3.0 * math.sin(progress * math.pi * 6) * math.exp(-progress * 1.5)
                dut_s21_value = dut_base + ripple
                dut_s21_value = max(-89.64, min(-0.39, dut_s21_value))
                dut_s21_db.append(dut_s21_value)
                
                # Fixture S21: Excellent performance from -0.21 to -0.30 dB
                fixture_base = -0.21 - 0.09 * progress
                variation = 0.01 * math.sin(progress * math.pi * 15)
                fixture_s21_value = fixture_base + variation
                fixture_s21_value = max(-0.30, min(-0.21, fixture_s21_value))
                fixture_s21_db.append(fixture_s21_value)
            
            print(f"Generated demo data: {len(frequencies)} points")
            
            response_data = {
                'dut': {
                    'frequencies': frequencies,
                    's21_db': dut_s21_db,
                    'filename': 'SfFilterSb.s2p',
                    'success': True,
                    'method': 'pure_python_demo',
                    'freq_unit': 'GHz',
                    'data_points': len(frequencies),
                    'freq_range': f"{frequencies[0]:.2f} - {frequencies[-1]:.2f} GHz",
                    's21_range': f"{min(dut_s21_db):.2f} to {max(dut_s21_db):.2f} dB"
                },
                'fixture': {
                    'frequencies': frequencies,
                    's21_db': fixture_s21_db,
                    'filename': 'SfSb.s2p',
                    'success': True,
                    'method': 'pure_python_demo',
                    'freq_unit': 'GHz',
                    'data_points': len(frequencies),
                    'freq_range': f"{frequencies[0]:.2f} - {frequencies[-1]:.2f} GHz",
                    's21_range': f"{min(fixture_s21_db):.2f} to {max(fixture_s21_db):.2f} dB"
                }
            }
            
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps(response_data)
            }
        
        # File upload endpoint - REAL SNP processing using rf_process7.py method
        if '/upload-snp' in path and method == 'POST':
            print("Processing real SNP file upload using rf_process7.py method")
            
            try:
                # Extract file content from request
                body = event.get('body', '')
                if event.get('isBase64Encoded', False):
                    body = base64.b64decode(body).decode('utf-8')
                
                # Parse the multipart form data to extract file content
                file_content = extract_file_from_form_data(body)
                
                if not file_content:
                    return {
                        'statusCode': 400,
                        'headers': headers,
                        'body': json.dumps({
                            'error': 'No file content found in request',
                            'success': False
                        })
                    }
                
                print(f"Processing SNP file content: {len(file_content)} characters")
                
                # Process SNP file using simplified ntwk_1.py method
                result = process_snp_file(file_content)
                
                if result['success']:
                    print(f"Successfully processed SNP file: {len(result['frequencies'])} points")
                    return {
                        'statusCode': 200,
                        'headers': headers,
                        'body': json.dumps(result)
                    }
                else:
                    return {
                        'statusCode': 400,
                        'headers': headers,
                        'body': json.dumps(result)
                    }
                
            except Exception as e:
                print(f"File upload error: {e}")
                import traceback
                traceback.print_exc()
                
                return {
                    'statusCode': 500,
                    'headers': headers,
                    'body': json.dumps({
                        'error': f'File processing failed: {str(e)}',
                        'success': False,
                        'type': 'snp_processing_error'
                    })
                }
        
        # Default response
        return {
            'statusCode': 404,
            'headers': headers,
            'body': json.dumps({
                'error': 'Endpoint not found',
                'path': path,
                'method': method,
                'available_endpoints': ['/api/health', '/api/demo-files', '/api/upload-snp']
            })
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({
                'error': str(e),
                'type': 'lambda_error'
            })
        }


def extract_file_from_form_data(body):
    """
    Extract file content from multipart form data
    """
    try:
        # Simple extraction for multipart form data
        # Look for file content between boundaries
        lines = body.split('\n')
        file_content_lines = []
        in_file_content = False
        
        for line in lines:
            if 'Content-Disposition: form-data' in line and 'filename=' in line:
                in_file_content = True
                continue
            elif line.startswith('--') and in_file_content:
                break
            elif in_file_content and line.strip():
                if not line.startswith('Content-'):
                    file_content_lines.append(line)
        
        return '\n'.join(file_content_lines)
    except Exception as e:
        print(f"Error extracting file content: {e}")
        return None


def process_snp_file(file_content):
    """
    Process SNP file using simplified ntwk_1.py method
    Based on rf_process7.py approach but without numpy
    """
    try:
        lines = file_content.strip().split('\n')
        
        # Parse header using regex (same as ntwk_1.py line 22)
        header_pattern = re.compile(r"#\s+(\w+)\s+(\w+)\s+(\w+)\s+R\s+(\d+)")
        header = None
        
        for line in lines:
            if line.strip().startswith('#'):
                header = header_pattern.search(line.strip())
                break
        
        if not header:
            return {
                'error': 'No valid header found in SNP file',
                'success': False
            }
        
        # Extract values from header (same as ntwk_1.py line 34-39)
        unit = header.group(1).lower()
        param_type = header.group(3).lower()  # 'ma' or 'ri'
        impedance = float(header.group(4))
        
        # Determine frequency scale (same as ntwk_1.py line 39)
        scale_map = {'hz': 1, 'khz': 1e3, 'mhz': 1e6, 'ghz': 1e9}
        scale = scale_map.get(unit, 1)
        
        print(f"SNP file header: unit={unit}, param_type={param_type}, impedance={impedance}, scale={scale}")
        
        # Process data lines (simplified version of ntwk_1.py compile_frequency_data)
        frequencies = []
        s21_db_values = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('#') or line.startswith('!') or not line:
                continue
            
            parts = line.split()
            if len(parts) < 3:  # Need at least frequency + 2 S-parameter values
                continue
            
            try:
                # Extract frequency (same as ntwk_1.py line 76)
                freq = float(parts[0]) * scale / 1e9  # Convert to GHz
                frequencies.append(freq)
                
                # Extract S21 parameter (simplified)
                if param_type == 'ri':
                    # Real/Imaginary format: S11_real, S11_imag, S21_real, S21_imag, ...
                    if len(parts) >= 4:
                        s21_real = float(parts[2])
                        s21_imag = float(parts[3])
                        s21_magnitude = math.sqrt(s21_real**2 + s21_imag**2)
                    else:
                        s21_magnitude = 0.001  # Fallback
                elif param_type == 'ma':
                    # Magnitude/Angle format: S11_mag, S11_angle, S21_mag, S21_angle, ...
                    if len(parts) >= 3:
                        s21_magnitude = float(parts[2])
                    else:
                        s21_magnitude = 0.001  # Fallback
                else:
                    s21_magnitude = 0.001  # Fallback
                
                # Convert to dB (same as ntwk_1.py s_db property line 151)
                if s21_magnitude > 0:
                    s21_db = 20 * math.log10(s21_magnitude)
                else:
                    s21_db = -100  # Very low value for zero magnitude
                
                s21_db_values.append(s21_db)
                
            except (ValueError, IndexError) as e:
                print(f"Error processing line: {line}, error: {e}")
                continue
        
        if not frequencies:
            return {
                'error': 'No valid frequency data found in SNP file',
                'success': False
            }
        
        print(f"Processed SNP file: {len(frequencies)} points, {min(frequencies):.2f}-{max(frequencies):.2f} GHz")
        print(f"S21 range: {min(s21_db_values):.2f} to {max(s21_db_values):.2f} dB")
        
        # Return in same format as demo files
        return {
            'frequencies': frequencies,
            's21_db': s21_db_values,
            'filename': 'uploaded_file.snp',
            'success': True,
            'method': 'real_snp_processing_rf_process7_style',
            'freq_unit': 'GHz',
            'data_points': len(frequencies),
            'freq_range': f"{min(frequencies):.2f} - {max(frequencies):.2f} GHz",
            's21_range': f"{min(s21_db_values):.2f} to {max(s21_db_values):.2f} dB",
            'original_unit': unit,
            'param_type': param_type,
            'impedance': impedance
        }
        
    except Exception as e:
        print(f"Error processing SNP file: {e}")
        import traceback
        traceback.print_exc()
        
        return {
            'error': f'SNP file processing failed: {str(e)}',
            'success': False,
            'type': 'snp_parsing_error'
        }
