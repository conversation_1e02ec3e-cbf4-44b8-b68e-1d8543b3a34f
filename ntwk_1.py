import numpy as np
import re
import matplotlib.pyplot as plt


class Network:
    def __init__(self, filename):
        self.filename = filename
        self.s_parameters = None
        self.z_parameters = None
        self.y_parameters = None
        self.frequency = None
        self.scale = 1
        self.number_of_ports = 0
        self.read_touchstone()

    def read_touchstone(self):
        with open(self.filename, 'r') as file:
            content = file.readlines()

        # Parse header using regex
        header_pattern = re.compile(r"#\s+(\w+)\s+(\w+)\s+(\w+)\s+R\s+(\d+)")
        header = None
        for line in content:
            if line.strip().startswith('#'):
                header = header_pattern.search(line.strip())
                break

        if not header:
            raise ValueError("No valid header found in the Touchstone file.")

        # print(header)
        # Extract values from header
        unit = header.group(1).lower()
        param_type = header.group(3).lower()  # 'ma' (magnitude-angle) or 'ri' (real-imaginary)
        impedance = float(header.group(4))

        # Determine frequency scale based on the unit
        scale = {'hz': 1, 'khz': 1e3, 'mhz': 1e6, 'ghz': 1e9}.get(unit, 1)
        # print(f'{self.filename}\nscale: {self.scale}, param_type: {param_type}, impedance: {impedance}')

        # # Read data
        # freqs = []
        # s_params = []
        # for line in content:
        #     if line.startswith('#') or line.strip().startswith('!'):
        #         continue
        #     # Ensure the line contains numeric data before processing
        #     parts = line.split()
        #     if not parts or not re.match(r"^-?\d+(\.\d+)?$", parts[0]):  # Ensure first part is a valid number
        #         continue
        #     print('l51', parts)
        #     freq = float(parts[0]) * self.scale
        #     freqs.append(freq)
        #     print(f'freq = {freq}')
        #
        #     if param_type == 'ri':
        #         s_params_line = np.array([complex(float(parts[i]), float(parts[i + 1])) for i in range(1, len(parts), 2)])
        #     elif param_type == 'ma':
        #         s_params_line = np.array([np.abs(float(parts[i])) * np.exp(1j * np.radians(float(parts[i + 1]))) for i in range(1, len(parts), 2)])
        #
        #     print(f's_param_line: {s_params_line}')
        #     s_params.append(s_params_line)


        # Read data
        # Process lines to gather all data for each frequency into a single line
        compiled_lines = self.compile_frequency_data(content)
        # print(compiled_lines[:5])

        freqs, s_params = [], []
        for i, line in enumerate(compiled_lines):
            # print(i, line)
            parts = line.split()
            # print(parts)
            freq = float(parts[0]) * scale
            freqs.append(freq)

            if param_type == 'ri':
                data = [complex(float(parts[i]), float(parts[i + 1])) for i in range(1, len(parts), 2)]
            elif param_type == 'ma':
                data = [np.abs(float(parts[i])) * np.exp(1j * np.radians(float(parts[i + 1]))) for i in range(1, len(parts), 2)]
            s_params.append(data)


        self.frequency = np.array(freqs)
        self.s_parameters = np.array(s_params).reshape(-1, int(np.sqrt(len(s_params[0]))), int(np.sqrt(len(s_params[0]))))
        self.number_of_ports = int(np.sqrt(len(s_params[0])))

        # Calculate Z and Y parameters
        # self.z_parameters = np.linalg.inv(self.s_parameters - np.eye(self.number_of_ports)) * impedance
        # self.y_parameters = np.linalg.inv(self.z_parameters)
        # print(f'number of ports: {self.number_of_ports}')
        # print(self.s_parameters[:5])
        self.z_parameters = np.array([self.s2z(S, impedance) for S in self.s_parameters])
        self.y_parameters = np.array([self.s2y(S, impedance) for S in self.s_parameters])

    def s2z(self, s, z0):
        """ Convert S-parameters to Z-parameters """
        Id = np.eye(len(s))
        return z0 * (Id + s) @ np.linalg.inv(Id - s)

    def s2y(self, s, z0):
        """ Convert S-parameters to Y-parameters """
        Id = np.eye(len(s))
        return 1/z0 * (Id - s) @ np.linalg.inv(Id + s)

    def compile_frequency_data(self, content):
        # compiled_data = {}
        # current_freq = None
        # for line in content:
        #     if line.startswith('#') or line.strip().startswith('!'):
        #         continue
        #     if re.match(r"^\s*\d", line):
        #         current_freq = line.split()[0]
        #         if current_freq not in compiled_data:
        #             compiled_data[current_freq] = []
        #         # print(f'l103: {line}\{compiled_data}')
        #         compiled_data[current_freq].extend(line.split()[1:])
        #     elif current_freq and (line.startswith(' ') or line.startswith('\t')):
        #         compiled_data[current_freq].extend(line.split())
        #         # print(f'{compiled_data}')
        # # Join all data pieces into single lines per frequency
        # return [f"{freq} {' '.join(data)}" for freq, data in compiled_data.items()]

        compiled_data = {}
        current_freq = None
        for line in content:
            # line = line.strip()
            if line.startswith('#') or line.startswith('!'):
                continue  # Skip comments and headers
            if current_freq and (line.startswith(' ') or line.startswith('\t')):
                # This line is a continuation of the current frequency
                parts = line.split()
                compiled_data[current_freq].extend(parts)
            elif re.match(r"^[0-9.]+", line):
                # A new frequency data starts here
                parts = line.split()
                current_freq = parts[0]  # The first part is the frequency
                if current_freq not in compiled_data:
                    compiled_data[current_freq] = []
                # Extend with the rest of the data on the line except the frequency part
                compiled_data[current_freq].extend(parts[1:])


        # Join all data pieces into single lines per frequency
        return [f"{freq} {' '.join(data)}" for freq, data in compiled_data.items()]

    @property
    def s_db(self):
        return 20 * np.log10(np.abs(self.s_parameters))

    @property
    def s(self):
        return self.s_parameters

    @property
    def y(self):
        return self.y_parameters

    @property
    def z(self):
        return self.z_parameters

    @property
    def freq(self):
        return self.frequency

    def plot_s_db(self, m, n, ax=None, color='blue', show_legend=True):
        if ax is None:
            ax = plt.gca()
        line, = ax.plot(self.frequency / 1e9, 20 * np.log10(np.abs(self.s_parameters[:, m, n])), color=color,
                        label=f'S[{m + 1},{n + 1}]')
        if show_legend:
            ax.legend()
        ax.set_title(f'S-Parameter S[{m + 1},{n + 1}] Magnitude (dB)')
        ax.set_xlabel('Frequency (GHz)')
        ax.set_ylabel('Magnitude (dB)')
        # plt.show()


def main():
    filename = r'C:\Users\<USER>\PycharmProjects\pythonProject\rf_process\CW_28AWG2Drain.s4p'
    ntwk = Network(filename)
    ntwk.plot_s_db(1, 1)
    # print(ntwk.s[:5])
    # print(ntwk.y_parameters[:5])
    # print(ntwk.z_parameters[:5])


if __name__ == "__main__":
    main()