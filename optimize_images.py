#!/usr/bin/env python3
"""
Image optimization script for better web performance
"""

import os
import subprocess
from pathlib import Path
from PIL import Image, ImageEnhance, ImageFilter
import json

def check_dependencies():
    """Check if required tools are available"""
    try:
        from PIL import Image
        return True
    except ImportError:
        print("❌ PIL (Pillow) not found. Install with: pip install Pillow")
        return False

def analyze_images():
    """Analyze current images and provide optimization recommendations"""
    print("🔍 Analyzing images...")
    print("-" * 25)
    
    image_dir = Path("Website-Images2")
    if not image_dir.exists():
        print("❌ Website-Images2 directory not found")
        return {}
    
    analysis = {
        'total_images': 0,
        'total_size': 0,
        'large_images': [],
        'recommendations': []
    }
    
    for img_path in image_dir.rglob("*"):
        if img_path.is_file() and img_path.suffix.lower() in ['.jpg', '.jpeg', '.png']:
            analysis['total_images'] += 1
            size = img_path.stat().st_size
            analysis['total_size'] += size
            
            # Check image dimensions and size
            try:
                with Image.open(img_path) as img:
                    width, height = img.size
                    
                    # Flag large images
                    if size > 500 * 1024:  # > 500KB
                        analysis['large_images'].append({
                            'path': str(img_path),
                            'size_kb': size // 1024,
                            'dimensions': f"{width}x{height}",
                            'format': img.format
                        })
                    
                    # Recommendations based on usage
                    if 'Hero' in img_path.name:
                        if width > 1920 or height > 1080:
                            analysis['recommendations'].append(f"Resize {img_path.name} to max 1920x1080 for hero images")
                    elif 'Service' in img_path.name:
                        if width > 800 or height > 600:
                            analysis['recommendations'].append(f"Resize {img_path.name} to max 800x600 for service icons")
                    
            except Exception as e:
                print(f"⚠️  Could not analyze {img_path}: {e}")
    
    return analysis

def optimize_image(input_path, output_path, target_width=None, target_height=None, quality=85):
    """Optimize a single image"""
    try:
        with Image.open(input_path) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create white background for transparency
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # Resize if target dimensions provided
            if target_width or target_height:
                original_width, original_height = img.size
                
                if target_width and target_height:
                    # Resize to exact dimensions
                    img = img.resize((target_width, target_height), Image.Resampling.LANCZOS)
                elif target_width:
                    # Resize maintaining aspect ratio based on width
                    ratio = target_width / original_width
                    new_height = int(original_height * ratio)
                    img = img.resize((target_width, new_height), Image.Resampling.LANCZOS)
                elif target_height:
                    # Resize maintaining aspect ratio based on height
                    ratio = target_height / original_height
                    new_width = int(original_width * ratio)
                    img = img.resize((new_width, target_height), Image.Resampling.LANCZOS)
            
            # Enhance image slightly
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(1.1)  # Slight sharpening
            
            # Save optimized image
            img.save(output_path, 'JPEG', quality=quality, optimize=True)
            
            return True
            
    except Exception as e:
        print(f"❌ Error optimizing {input_path}: {e}")
        return False

def create_optimized_images():
    """Create optimized versions of images"""
    print("\n🚀 Creating optimized images...")
    print("-" * 35)
    
    # Create optimized directory
    optimized_dir = Path("Website-Images2-Optimized")
    optimized_dir.mkdir(exist_ok=True)
    
    # Copy directory structure
    source_dir = Path("Website-Images2")
    if not source_dir.exists():
        print("❌ Website-Images2 directory not found")
        return False
    
    optimization_rules = {
        # Hero images - large, high quality
        'Hero': {'width': 1920, 'height': 1080, 'quality': 90},
        # Service images - medium size
        'Service': {'width': 800, 'height': 600, 'quality': 85},
        # About images - medium size
        'About': {'width': 1200, 'height': 800, 'quality': 85},
        # CTA images - medium size, can be compressed more
        'CTA': {'width': 1200, 'height': 800, 'quality': 80},
        # Testimonial images - small
        'Testimonial': {'width': 400, 'height': 400, 'quality': 85},
        # Contact images - medium
        'Contact': {'width': 1200, 'height': 800, 'quality': 85}
    }
    
    optimized_count = 0
    total_original_size = 0
    total_optimized_size = 0
    
    for img_path in source_dir.rglob("*"):
        if img_path.is_file() and img_path.suffix.lower() in ['.jpg', '.jpeg', '.png']:
            # Determine optimization rule
            rule = None
            for key, settings in optimization_rules.items():
                if key in img_path.name:
                    rule = settings
                    break
            
            if not rule:
                # Default optimization
                rule = {'width': 1200, 'height': 800, 'quality': 85}
            
            # Create output path
            relative_path = img_path.relative_to(source_dir)
            output_path = optimized_dir / relative_path.with_suffix('.jpg')
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Get original size
            original_size = img_path.stat().st_size
            total_original_size += original_size
            
            # Optimize image
            print(f"   🔧 Optimizing: {img_path.name}")
            success = optimize_image(
                img_path, 
                output_path,
                target_width=rule.get('width'),
                target_height=rule.get('height'),
                quality=rule['quality']
            )
            
            if success:
                optimized_size = output_path.stat().st_size
                total_optimized_size += optimized_size
                reduction = (1 - optimized_size / original_size) * 100
                print(f"      ✅ {original_size//1024}KB → {optimized_size//1024}KB ({reduction:.1f}% reduction)")
                optimized_count += 1
            else:
                print(f"      ❌ Failed to optimize")
    
    # Summary
    total_reduction = (1 - total_optimized_size / total_original_size) * 100 if total_original_size > 0 else 0
    
    print(f"\n📊 Optimization Summary:")
    print(f"   ✅ Optimized: {optimized_count} images")
    print(f"   📉 Size reduction: {total_reduction:.1f}%")
    print(f"   💾 Original: {total_original_size//1024//1024:.1f}MB")
    print(f"   💾 Optimized: {total_optimized_size//1024//1024:.1f}MB")
    print(f"   💾 Saved: {(total_original_size-total_optimized_size)//1024//1024:.1f}MB")
    
    return optimized_count > 0

def update_references_to_optimized():
    """Update website to use optimized images"""
    print("\n🔄 Updating references to optimized images...")
    print("-" * 45)
    
    files_to_update = ['index.html', 'about.html', 'services.html', 'contact.html', 'css/style.css']
    
    for file_path in files_to_update:
        if not Path(file_path).exists():
            continue
            
        print(f"📝 Updating: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace Website-Images2 with Website-Images2-Optimized
        updated_content = content.replace('Website-Images2/', 'Website-Images2-Optimized/')
        updated_content = updated_content.replace('../Website-Images2/', '../Website-Images2-Optimized/')
        
        # Also ensure all image extensions are .jpg
        updated_content = updated_content.replace('.png', '.jpg')
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"   ✅ Updated references")

def main():
    """Main optimization function"""
    print("🚀 Image Optimization Tool")
    print("=" * 35)
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Analyze current images
    analysis = analyze_images()
    
    if analysis['total_images'] == 0:
        print("❌ No images found in Website-Images2")
        return
    
    print(f"\n📊 Current Status:")
    print(f"   📸 Total images: {analysis['total_images']}")
    print(f"   💾 Total size: {analysis['total_size']//1024//1024:.1f}MB")
    print(f"   ⚠️  Large images: {len(analysis['large_images'])}")
    
    if analysis['large_images']:
        print(f"\n📋 Large images (>500KB):")
        for img in analysis['large_images']:
            print(f"   📸 {Path(img['path']).name}: {img['size_kb']}KB ({img['dimensions']})")
    
    if analysis['recommendations']:
        print(f"\n💡 Recommendations:")
        for rec in analysis['recommendations']:
            print(f"   • {rec}")
    
    # Ask if user wants to optimize
    print(f"\n🔧 Optimization will:")
    print(f"   • Resize images to appropriate dimensions")
    print(f"   • Compress images for web")
    print(f"   • Convert all to JPEG format")
    print(f"   • Create optimized versions in Website-Images2-Optimized/")
    
    proceed = input(f"\nProceed with optimization? (y/n): ").lower().strip()
    if proceed != 'y':
        print("👋 Optimization cancelled!")
        return
    
    # Create optimized images
    success = create_optimized_images()
    
    if success:
        # Ask if user wants to update references
        update_refs = input(f"\nUpdate website to use optimized images? (y/n): ").lower().strip()
        if update_refs == 'y':
            update_references_to_optimized()
            print(f"\n✅ Website updated to use optimized images!")
        
        print(f"\n🎉 Optimization complete!")
        print(f"📁 Optimized images saved in: Website-Images2-Optimized/")
    else:
        print(f"\n❌ Optimization failed!")

if __name__ == "__main__":
    main()
