# 🏗️ EDA Easy - Organized Project Structure

## 📁 Directory Structure

```
organized-project/
├── 📄 frontend/           # Frontend web application
│   ├── pages/            # HTML pages
│   ├── styles/           # CSS stylesheets
│   ├── scripts/          # JavaScript files
│   └── images/           # Images and favicon
│
├── 🐍 backend/            # Backend services
│   ├── lambda/           # AWS Lambda functions
│   ├── reference/        # Reference implementations
│   └── utils/            # Utility functions
│
├── 🚀 deployment/         # Deployment automation
│   ├── aws/              # AWS deployment scripts
│   ├── scripts/          # Utility scripts
│   └── configs/          # Configuration files
│
├── 📚 docs/               # Documentation
│   ├── design/           # Design documentation
│   ├── api/              # API documentation
│   └── user-guide/       # User guides
│
├── 🎨 assets/             # Static assets
│   ├── images/           # Image files
│   ├── videos/           # Video files
│   └── sample-files/     # Sample S-parameter files
│
└── 🧪 tests/              # Test files
    ├── unit/             # Unit tests
    ├── integration/      # Integration tests
    └── e2e/              # End-to-end tests
```

## 🚀 Quick Start

### Development
```bash
# Serve frontend locally
cd frontend/pages
python3 -m http.server 8000

# Deploy to AWS
cd deployment/aws
./deploy-aws.sh
```

### Testing
```bash
# Run tests
cd deployment/scripts
./test-deployment.sh
```

## 📚 Documentation

- **Design Documentation**: `docs/design/DESIGN_DOCUMENTATION.md`
- **API Documentation**: `docs/api/`
- **User Guide**: `docs/user-guide/`

## 🔧 Maintenance

- **Sync to AWS**: `deployment/aws/sync-to-aws.sh`
- **Cleanup AWS**: `deployment/aws/cleanup-aws.sh`
- **Fix Issues**: `deployment/scripts/fix-*.sh`
