/* De-embedding Trial Page Styles */

.main_trial {
  background: url("../Website-Images2/Services/Service-1.jpg") center / cover no-repeat;
  position: relative;
  min-height: 60vh;
}

.main_trial::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(48, 58, 77, 0.5));
  z-index: 1;
}

.main_trial .main__container {
  position: relative;
  z-index: 2;
  padding-top: 8rem;
  padding-bottom: 4rem;
}

.main_trial .main__title,
.main_trial .main__text {
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.main_trial .main__title {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem 2rem;
  border-radius: 12px;
  backdrop-filter: blur(5px);
  display: inline-block;
}

.main_trial .main__text {
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto;
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem 1.5rem;
  border-radius: 8px;
  backdrop-filter: blur(3px);
}

/* Trial Interface */
.trial-interface {
  padding: 4rem 0;
  background: #f8f9fa;
}

.trial-interface__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Step Indicator */
.trial-steps {
  margin-bottom: 3rem;
}

.step-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.step.active {
  opacity: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #6c757d;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: #303a4d;
  color: white;
}

.step-title {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.step.active .step-title {
  color: #303a4d;
  font-weight: 600;
}

/* Upload Section */
.upload-section {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.upload-section h2 {
  text-align: center;
  margin-bottom: 1rem;
  color: #303a4d;
  font-size: 2rem;
}

.upload-description {
  text-align: center;
  color: #6c757d;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.upload-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.upload-box h3 {
  margin-bottom: 1rem;
  color: #303a4d;
  font-size: 1.2rem;
}

.file-drop-zone {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.file-drop-zone:hover {
  border-color: #303a4d;
  background: #f8f9fa;
}

.file-drop-zone.dragover {
  border-color: #303a4d;
  background: #e3f2fd;
}

.upload-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.drop-content p {
  margin: 0.5rem 0;
  color: #6c757d;
}

.file-info {
  font-size: 0.9rem;
  color: #adb5bd;
}

.file-preview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background: #e8f5e8;
  border-radius: 6px;
  position: relative;
}

.file-info-row {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.file-icon {
  font-size: 1.5rem;
}

.file-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-name {
  font-weight: 600;
  color: #303a4d;
}

.file-size {
  font-size: 0.9rem;
  color: #6c757d;
}

.remove-file {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background 0.3s ease;
  align-self: flex-start;
}

.remove-file:hover {
  background: #c82333;
}

/* Demo Section */
.demo-section {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 2rem;
  margin-top: 2rem;
}

.demo-section h3 {
  margin-bottom: 1rem;
  color: #303a4d;
}

.demo-section p {
  margin-bottom: 1.5rem;
}

.demo-button {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

.demo-button:hover {
  background: #138496;
}

/* Action Buttons */
.action-buttons {
  text-align: center;
}

.button-primary {
  background: #303a4d;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.button-primary:hover:not(:disabled) {
  background: #252e3a;
  transform: translateY(-2px);
}

.button-primary:disabled {
  background: #adb5bd;
  cursor: not-allowed;
}

.button-secondary {
  background: transparent;
  color: #303a4d;
  border: 2px solid #303a4d;
  padding: 1rem 2rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.button-secondary:hover {
  background: #303a4d;
  color: white;
}

/* Processing Section */
.processing-section {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.processing-animation {
  margin-bottom: 2rem;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #303a4d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-steps {
  max-width: 400px;
  margin: 0 auto;
}

.process-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 6px;
}

.process-step.completed {
  background: #e8f5e8;
  color: #28a745;
}

.process-step.processing {
  background: #fff3cd;
  color: #856404;
}

.spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #856404;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Results Section */
.results-section {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.results-grid {
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 2rem;
}

.plot-container h3 {
  margin-bottom: 1rem;
  color: #303a4d;
}

.plot-area {
  height: 500px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.results-summary {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-stats {
  margin-bottom: 2rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
}

.stat-label {
  font-weight: 500;
  color: #6c757d;
}

.stat-value {
  font-weight: 600;
  color: #303a4d;
}

/* CTA Section */
.outro_trial {
  background: url("../Website-Images2/Services/CTA.jpg") center / cover no-repeat;
  position: relative;
}

.outro_trial::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(48, 58, 77, 0.7), rgba(0, 0, 0, 0.5));
  z-index: 1;
}

.outro_trial .outro__container {
  position: relative;
  z-index: 2;
}

.outro_trial .outro__title,
.outro_trial .outro__text {
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.outro__actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* Trial Button Styling */
.button-trial {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  margin-right: 1rem;
  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.button-trial:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

.services-page__actions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-grid {
    grid-template-columns: 1fr;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .step-indicator {
    gap: 1rem;
  }

  .main_trial .main__title {
    font-size: 2.5rem;
  }

  .outro__actions {
    flex-direction: column;
    align-items: center;
  }

  .services-page__actions {
    flex-direction: column;
    align-items: flex-start;
  }

  .button-trial {
    margin-right: 0;
    margin-bottom: 1rem;
  }
}
