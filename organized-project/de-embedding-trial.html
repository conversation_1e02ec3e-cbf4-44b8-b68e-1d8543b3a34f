<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>De-embedding Trial - EDA Solutions</title>
    <link rel="icon" type="image/png" href="../images/favicon.png">
    <link rel="stylesheet" href="../styles/style.css">
    <link rel="stylesheet" href="../styles/trial.css">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
</head>
<body>
    <div class="wrapper">
        <!-- Header -->
        <header class="header">
            <div class="header__container">
                <a href="index.html" class="header__logo">
                    <img src="images/logo.svg" alt="EDA Solutions">
                </a>
                <nav class="header__menu menu">
                    <div class="menu__body">
                        <ul class="menu__list">
                            <li class="menu__item">
                                <a href="index.html" class="menu__link">Home</a>
                            </li>
                            <li class="menu__item">
                                <a href="about.html" class="menu__link">About</a>
                            </li>
                            <li class="menu__item">
                                <a href="services.html" class="menu__link">Services</a>
                            </li>
                            <li class="menu__item">
                                <a href="pricing.html" class="menu__link">Pricing</a>
                            </li>
                            <li class="menu__item">
                                <a href="contact.html" class="menu__link">Contact</a>
                            </li>
                        </ul>
                    </div>
                    <button type="button" class="menu__icon icon-menu">
                        <span></span>
                    </button>
                </nav>
                <div class="header__actions actions-header">
                    <a href="contact.html" class="actions-header__button button">Get Started</a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="page">
            <!-- Hero Section -->
            <section class="main main_trial">
                <div class="main__container">
                    <div class="main__content">
                        <h1 class="main__title">De-embedding Trial</h1>
                        <p class="main__text">
                            Experience our advanced de-embedding technology. Upload your S-parameter files
                            and see real-time de-embedding results with professional-grade accuracy.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Trial Interface -->
            <section class="trial-interface">
                <div class="trial-interface__container">
                    <div class="trial-steps">
                        <div class="step-indicator">
                            <div class="step active" data-step="1">
                                <span class="step-number">1</span>
                                <span class="step-title">Upload Files</span>
                            </div>
                            <div class="step" data-step="2">
                                <span class="step-number">2</span>
                                <span class="step-title">Process</span>
                            </div>
                            <div class="step" data-step="3">
                                <span class="step-number">3</span>
                                <span class="step-title">Results</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: File Upload -->
                    <div class="trial-step" id="step1">
                        <div class="upload-section">
                            <h2>Upload S-Parameter Files</h2>
                            <p class="upload-description">
                                Upload your fixture-DUT-fixture and fixture-fixture S-parameter files (.s2p, .s4p, etc.)
                            </p>

                            <div class="upload-grid">
                                <div class="upload-box">
                                    <h3>Fixture-DUT-Fixture File</h3>
                                    <div class="file-drop-zone" id="dut-drop-zone">
                                        <div class="drop-content">
                                            <div class="upload-icon">📁</div>
                                            <p>Drop your fixture-DUT-fixture .snp file here</p>
                                            <p class="file-info">or click to browse</p>
                                            <input type="file" id="dut-file" accept=".s2p,.s4p,.s6p,.s8p,.snp" hidden>
                                        </div>
                                        <div class="file-preview" id="dut-preview" style="display: none;">
                                            <div class="file-info-row">
                                                <div class="file-icon">✅</div>
                                                <div class="file-details">
                                                    <span class="file-name"></span>
                                                    <span class="file-size"></span>
                                                </div>
                                            </div>
                                            <button class="remove-file" onclick="window.trialInterface.removeFile('dut')" title="Remove file">🗑️ Remove File</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="upload-box">
                                    <h3>Fixture-Fixture File</h3>
                                    <div class="file-drop-zone" id="fixture-drop-zone">
                                        <div class="drop-content">
                                            <div class="upload-icon">📁</div>
                                            <p>Drop your fixture-fixture .snp file here</p>
                                            <p class="file-info">or click to browse</p>
                                            <input type="file" id="fixture-file" accept=".s2p,.s4p,.s6p,.s8p,.snp" hidden>
                                        </div>
                                        <div class="file-preview" id="fixture-preview" style="display: none;">
                                            <div class="file-info-row">
                                                <div class="file-icon">✅</div>
                                                <div class="file-details">
                                                    <span class="file-name"></span>
                                                    <span class="file-size"></span>
                                                </div>
                                            </div>
                                            <button class="remove-file" onclick="window.trialInterface.removeFile('fixture')" title="Remove file">🗑️ Remove File</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="action-buttons">
                                <button class="button button-primary" id="process-btn" onclick="window.trialInterface.processFiles()" disabled>
                                    Start De-embedding
                                </button>
                            </div>

                            <div class="demo-section">
                                <h3>Try Demo Files</h3>
                                <p>Don't have files? Use our demo S-parameter files to see the de-embedding in action.</p>
                                <button class="demo-button" onclick="window.trialInterface.loadDemoFiles()">Load Demo Files</button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Processing -->
                    <div class="trial-step" id="step2" style="display: none;">
                        <div class="processing-section">
                            <h2>Processing De-embedding</h2>
                            <div class="processing-animation">
                                <div class="spinner"></div>
                                <p>Performing advanced de-embedding calculations...</p>
                            </div>
                            <div class="processing-steps">
                                <div class="process-step completed">
                                    <span class="check">✓</span> Files validated
                                </div>
                                <div class="process-step processing">
                                    <span class="spinner-small"></span> Computing de-embedding matrices
                                </div>
                                <div class="process-step">
                                    <span class="pending">○</span> Generating results
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Results -->
                    <div class="trial-step" id="step3" style="display: none;">
                        <div class="results-section">
                            <div class="results-grid">
                                <div class="plot-container">
                                    <div id="s21-plot" class="plot-area"></div>
                                </div>
                                <div class="results-summary">
                                    <div class="action-buttons">
                                        <button class="button button-secondary" onclick="window.trialInterface.downloadResults()" style="margin-bottom: 20px;">
                                            Download S21 Data
                                        </button>
                                        <button class="button button-primary" onclick="window.trialInterface.resetTrial()">
                                            Try Another File
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- CTA Section -->
            <section class="outro outro_trial">
                <div class="outro__container">
                    <div class="outro__content">
                        <h2 class="outro__title">Ready for Professional De-embedding?</h2>
                        <p class="outro__text">
                            Experience the full power of our de-embedding solutions with advanced features,
                            batch processing, and enterprise-grade accuracy.
                        </p>
                        <div class="outro__actions">
                            <a href="contact.html" class="button button-primary">Get Full Version</a>
                            <a href="services.html" class="button button-secondary">Learn More</a>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer__container">
                <div class="footer__content">
                    <div class="footer__logo">
                        <img src="images/logo.svg" alt="EDA Solutions">
                    </div>
                    <nav class="footer__nav">
                        <a href="index.html">Home</a>
                        <a href="about.html">About</a>
                        <a href="services.html">Services</a>
                        <a href="contact.html">Contact</a>
                    </nav>
                </div>
                <div class="footer__bottom">
                    <p>&copy; 2024 EDA Solutions. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    <script src="../scripts/trial.js"></script>
    <script src="../scripts/de-embedding.js"></script>
    <script src="../scripts/snp-parser.js"></script>
</body>
</html>
