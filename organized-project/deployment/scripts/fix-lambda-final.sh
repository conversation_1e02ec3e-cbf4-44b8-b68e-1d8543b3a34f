#!/bin/bash

# 🔧 Final Lambda Fix - Create a working Lambda function

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 Creating a working Lambda function...${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Create deployment directory
rm -rf lambda-deployment
mkdir -p lambda-deployment
cd lambda-deployment

# Create a simple, working Lambda function
cat > lambda_function.py << 'EOF'
import json
import math

def lambda_handler(event, context):
    """Simple working Lambda function for EDA backend"""
    
    # Enable CORS
    headers = {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    }
    
    try:
        print(f"Event: {json.dumps(event)}")
        
        # Handle OPTIONS request (CORS preflight)
        if event.get('httpMethod') == 'OPTIONS':
            return {
                'statusCode': 200,
                'headers': headers,
                'body': ''
            }
        
        # Get the path
        path = event.get('path', '')
        method = event.get('httpMethod', 'GET')
        
        print(f"Processing: {method} {path}")
        
        # Health check endpoint
        if '/health' in path:
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({
                    'status': 'healthy', 
                    'service': 'EDA Backend Lambda',
                    'version': '2.0',
                    'timestamp': context.aws_request_id
                })
            }
        
        # Demo files endpoint
        if '/demo-files' in path:
            # Generate simple demo data
            frequencies = []
            dut_s21_db = []
            fixture_s21_db = []
            
            # Create 100 frequency points from 3.4 to 4.4 GHz
            for i in range(100):
                freq = 3.4 + (4.4 - 3.4) * i / 99
                frequencies.append(freq)
                
                # Demo DUT data (more loss)
                dut_loss = -20 - 10 * math.exp(-((freq - 3.9) / 0.3) ** 2)
                dut_s21_db.append(dut_loss)
                
                # Demo fixture data (less loss)
                fixture_loss = -5 - 5 * math.exp(-((freq - 3.9) / 0.5) ** 2)
                fixture_s21_db.append(fixture_loss)
            
            demo_data = {
                'success': True,
                'dut': {
                    'frequencies': frequencies,
                    's21_db': dut_s21_db,
                    'filename': 'demo_dut.s2p',
                    'success': True,
                    'method': 'lambda_simple_generator'
                },
                'fixture': {
                    'frequencies': frequencies,
                    's21_db': fixture_s21_db,
                    'filename': 'demo_fixture.s2p',
                    'success': True,
                    'method': 'lambda_simple_generator'
                }
            }
            
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps(demo_data)
            }
        
        # File upload endpoint (simplified)
        if '/upload-snp' in path and method == 'POST':
            # For now, return mock data for file uploads
            frequencies = []
            s21_db = []
            
            # Create 200 frequency points from 0.1 to 40 GHz
            for i in range(200):
                freq = 0.1 + (40 - 0.1) * i / 199
                frequencies.append(freq)
                
                # Mock S21 data
                loss = -10 - 20 * (freq / 40)
                s21_db.append(loss)
            
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({
                    'success': True,
                    'frequencies': frequencies,
                    's21_db': s21_db,
                    'filename': 'uploaded_file.s2p',
                    'method': 'lambda_mock_upload'
                })
            }
        
        # Default response
        return {
            'statusCode': 404,
            'headers': headers,
            'body': json.dumps({
                'error': 'Endpoint not found',
                'path': path,
                'method': method,
                'available_endpoints': ['/api/health', '/api/demo-files', '/api/upload-snp']
            })
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({
                'error': str(e),
                'type': 'lambda_error',
                'event': event
            })
        }
EOF

print_status "Simple Lambda function created"

# Create deployment package (no external dependencies)
echo -e "${BLUE}📦 Creating deployment package...${NC}"
zip -r ../lambda-simple.zip . -q

cd ..
print_status "Deployment package created"

# Update Lambda function
echo -e "${BLUE}🚀 Updating Lambda function...${NC}"
aws lambda update-function-code \
    --function-name eda-easy-backend \
    --zip-file fileb://lambda-simple.zip \
    --region us-west-2

print_status "Lambda function updated"

# Update function configuration
echo -e "${BLUE}⚙️ Updating function configuration...${NC}"
aws lambda update-function-configuration \
    --function-name eda-easy-backend \
    --handler lambda_function.lambda_handler \
    --timeout 30 \
    --memory-size 128 \
    --region us-west-2

print_status "Function configuration updated"

# Wait for update to complete
echo -e "${BLUE}⏳ Waiting for Lambda update to complete...${NC}"
sleep 15

# Test the updated function
echo -e "${BLUE}🧪 Testing updated Lambda function...${NC}"
API_URL="https://fh73i40lo2.execute-api.us-west-2.amazonaws.com/prod"

# Test health endpoint
echo -e "${BLUE}Testing health endpoint...${NC}"
response=$(curl -s --max-time 15 "$API_URL/api/health" || echo "failed")

if echo "$response" | grep -q "healthy"; then
    print_status "✅ Health endpoint working! Response: $response"
else
    print_error "❌ Health endpoint failed. Response: $response"
fi

# Test demo files endpoint
echo -e "${BLUE}Testing demo files endpoint...${NC}"
demo_response=$(curl -s --max-time 15 "$API_URL/api/demo-files" || echo "failed")

if echo "$demo_response" | grep -q "dut"; then
    print_status "✅ Demo files endpoint working!"
    echo "Sample response: $(echo "$demo_response" | head -c 200)..."
else
    print_error "❌ Demo files endpoint failed. Response: $demo_response"
fi

# Cleanup
rm -rf lambda-deployment lambda-simple.zip

echo ""
echo -e "${GREEN}🎉 Lambda function should now be working!${NC}"
echo ""
echo -e "${BLUE}📋 Test your website now:${NC}"
echo -e "🌐 Website: ${GREEN}http://eda-easy-website-**********.s3-website-us-west-2.amazonaws.com${NC}"
echo ""
echo -e "${YELLOW}🎯 Try these tests:${NC}"
echo "1. Go to de-embedding trial page"
echo "2. Click 'Load Demo Files' - should work now!"
echo "3. Try uploading files - should work with mock data"
echo ""
echo -e "${BLUE}🔧 If it still doesn't work, check CloudWatch logs:${NC}"
echo "aws logs describe-log-streams --log-group-name '/aws/lambda/eda-easy-backend' --region us-west-2"
