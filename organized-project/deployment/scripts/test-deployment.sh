#!/bin/bash

# 🧪 EDA Easy Website - Automated Testing Script
# This script tests the deployed website and backend functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test configuration
WEBSITE_URL=""
API_URL=""
TIMEOUT=30

# Function to print test results
print_test_pass() {
    echo -e "${GREEN}✅ PASS: $1${NC}"
}

print_test_fail() {
    echo -e "${RED}❌ FAIL: $1${NC}"
}

print_test_info() {
    echo -e "${BLUE}ℹ️  INFO: $1${NC}"
}

print_test_warning() {
    echo -e "${YELLOW}⚠️  WARN: $1${NC}"
}

# Get URLs from user or deployment output
get_urls() {
    if [ -z "$WEBSITE_URL" ]; then
        echo -e "${BLUE}Please enter your website URL:${NC}"
        read -p "Website URL: " WEBSITE_URL
    fi
    
    if [ -z "$API_URL" ]; then
        echo -e "${BLUE}Please enter your API URL:${NC}"
        read -p "API URL: " API_URL
    fi
}

# Test HTTP response
test_http_response() {
    local url=$1
    local expected_code=$2
    local test_name=$3
    
    print_test_info "Testing: $test_name"
    
    response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$url" || echo "000")
    
    if [ "$response" = "$expected_code" ]; then
        print_test_pass "$test_name - HTTP $response"
        return 0
    else
        print_test_fail "$test_name - Expected HTTP $expected_code, got $response"
        return 1
    fi
}

# Test JSON API response
test_json_api() {
    local url=$1
    local test_name=$2
    local expected_key=$3
    
    print_test_info "Testing: $test_name"
    
    response=$(curl -s --max-time $TIMEOUT "$url" || echo "{}")
    
    if echo "$response" | jq -e ".$expected_key" > /dev/null 2>&1; then
        print_test_pass "$test_name - JSON response valid"
        return 0
    else
        print_test_fail "$test_name - Invalid JSON or missing key '$expected_key'"
        echo "Response: $response"
        return 1
    fi
}

# Test website content
test_website_content() {
    local url=$1
    local expected_text=$2
    local test_name=$3
    
    print_test_info "Testing: $test_name"
    
    content=$(curl -s --max-time $TIMEOUT "$url" || echo "")
    
    if echo "$content" | grep -q "$expected_text"; then
        print_test_pass "$test_name - Content found"
        return 0
    else
        print_test_fail "$test_name - Expected content not found: '$expected_text'"
        return 1
    fi
}

# Main testing function
run_tests() {
    local total_tests=0
    local passed_tests=0
    
    echo -e "${BLUE}🧪 Starting EDA Easy Deployment Tests...${NC}"
    echo ""
    
    # Test 1: Website Homepage
    echo -e "${BLUE}📱 Testing Website Frontend...${NC}"
    
    ((total_tests++))
    if test_http_response "$WEBSITE_URL" "200" "Homepage accessibility"; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_website_content "$WEBSITE_URL" "EDA Easy" "Homepage title"; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_website_content "$WEBSITE_URL" "De-embedding" "De-embedding service"; then
        ((passed_tests++))
    fi
    
    # Test 2: Website Pages
    pages=("about.html" "services.html" "pricing.html" "contact.html" "de-embedding-trial.html")
    
    for page in "${pages[@]}"; do
        ((total_tests++))
        if test_http_response "$WEBSITE_URL/$page" "200" "Page: $page"; then
            ((passed_tests++))
        fi
    done
    
    # Test 3: Static Assets
    echo ""
    echo -e "${BLUE}🎨 Testing Static Assets...${NC}"
    
    ((total_tests++))
    if test_http_response "$WEBSITE_URL/css/style.css" "200" "Main CSS file"; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_http_response "$WEBSITE_URL/css/trial.css" "200" "Trial CSS file"; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_http_response "$WEBSITE_URL/js/trial.js" "200" "Trial JavaScript file"; then
        ((passed_tests++))
    fi
    
    # Test 4: Backend API
    echo ""
    echo -e "${BLUE}🐍 Testing Backend API...${NC}"
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        print_test_warning "jq not installed. Installing for JSON testing..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y jq
        elif command -v yum &> /dev/null; then
            sudo yum install -y jq
        elif command -v brew &> /dev/null; then
            brew install jq
        else
            print_test_warning "Could not install jq. Skipping JSON tests."
        fi
    fi
    
    if command -v jq &> /dev/null; then
        ((total_tests++))
        if test_json_api "$API_URL/api/health" "Health check endpoint" "status"; then
            ((passed_tests++))
        fi
        
        ((total_tests++))
        if test_json_api "$API_URL/api/demo-files" "Demo files endpoint" "dut"; then
            ((passed_tests++))
        fi
    fi
    
    # Test 5: CORS Headers
    echo ""
    echo -e "${BLUE}🌐 Testing CORS Configuration...${NC}"
    
    ((total_tests++))
    cors_headers=$(curl -s -I --max-time $TIMEOUT "$API_URL/api/health" | grep -i "access-control-allow-origin" || echo "")
    if [ -n "$cors_headers" ]; then
        print_test_pass "CORS headers present"
        ((passed_tests++))
    else
        print_test_fail "CORS headers missing"
    fi
    
    # Test 6: Performance Tests
    echo ""
    echo -e "${BLUE}⚡ Testing Performance...${NC}"
    
    ((total_tests++))
    homepage_time=$(curl -s -o /dev/null -w "%{time_total}" --max-time $TIMEOUT "$WEBSITE_URL" || echo "999")
    if (( $(echo "$homepage_time < 3.0" | bc -l) )); then
        print_test_pass "Homepage load time: ${homepage_time}s (< 3s)"
        ((passed_tests++))
    else
        print_test_fail "Homepage load time: ${homepage_time}s (> 3s)"
    fi
    
    ((total_tests++))
    api_time=$(curl -s -o /dev/null -w "%{time_total}" --max-time $TIMEOUT "$API_URL/api/health" || echo "999")
    if (( $(echo "$api_time < 2.0" | bc -l) )); then
        print_test_pass "API response time: ${api_time}s (< 2s)"
        ((passed_tests++))
    else
        print_test_fail "API response time: ${api_time}s (> 2s)"
    fi
    
    # Test 7: Mobile Responsiveness
    echo ""
    echo -e "${BLUE}📱 Testing Mobile Responsiveness...${NC}"
    
    ((total_tests++))
    mobile_content=$(curl -s --max-time $TIMEOUT -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)" "$WEBSITE_URL" || echo "")
    if echo "$mobile_content" | grep -q "viewport"; then
        print_test_pass "Mobile viewport meta tag present"
        ((passed_tests++))
    else
        print_test_fail "Mobile viewport meta tag missing"
    fi
    
    # Test 8: Security Headers
    echo ""
    echo -e "${BLUE}🔒 Testing Security...${NC}"
    
    ((total_tests++))
    if test_http_response "$WEBSITE_URL" "200" "HTTPS redirect test"; then
        ((passed_tests++))
    fi
    
    # Test Results Summary
    echo ""
    echo -e "${BLUE}📊 Test Results Summary${NC}"
    echo "=================================="
    echo -e "Total Tests: ${BLUE}$total_tests${NC}"
    echo -e "Passed: ${GREEN}$passed_tests${NC}"
    echo -e "Failed: ${RED}$((total_tests - passed_tests))${NC}"
    
    success_rate=$(( passed_tests * 100 / total_tests ))
    echo -e "Success Rate: ${GREEN}$success_rate%${NC}"
    
    if [ $success_rate -ge 90 ]; then
        echo -e "${GREEN}🎉 Excellent! Deployment is working great!${NC}"
    elif [ $success_rate -ge 75 ]; then
        echo -e "${YELLOW}⚠️  Good! Minor issues detected.${NC}"
    else
        echo -e "${RED}❌ Issues detected. Please review failed tests.${NC}"
    fi
    
    # Recommendations
    echo ""
    echo -e "${BLUE}💡 Recommendations:${NC}"
    
    if [ $((total_tests - passed_tests)) -gt 0 ]; then
        echo "• Review failed tests and fix issues"
        echo "• Check AWS CloudWatch logs for errors"
        echo "• Verify DNS propagation if using custom domain"
    fi
    
    echo "• Set up monitoring with AWS CloudWatch"
    echo "• Configure SSL certificate for HTTPS"
    echo "• Set up automated backups"
    echo "• Consider CloudFront CDN for better performance"
    
    return $((total_tests - passed_tests))
}

# Load testing function
run_load_test() {
    echo -e "${BLUE}🚀 Running Load Test...${NC}"
    
    if ! command -v ab &> /dev/null; then
        print_test_warning "Apache Bench (ab) not installed. Skipping load test."
        return
    fi
    
    print_test_info "Testing with 100 requests, 10 concurrent"
    
    ab -n 100 -c 10 "$WEBSITE_URL/" > load_test_results.txt 2>&1
    
    if [ $? -eq 0 ]; then
        requests_per_second=$(grep "Requests per second" load_test_results.txt | awk '{print $4}')
        print_test_pass "Load test completed: $requests_per_second requests/second"
    else
        print_test_fail "Load test failed"
    fi
    
    rm -f load_test_results.txt
}

# Main execution
main() {
    # Check if URLs provided as arguments
    if [ $# -eq 2 ]; then
        WEBSITE_URL=$1
        API_URL=$2
    else
        get_urls
    fi
    
    # Validate URLs
    if [[ ! $WEBSITE_URL =~ ^https?:// ]]; then
        WEBSITE_URL="http://$WEBSITE_URL"
    fi
    
    if [[ ! $API_URL =~ ^https?:// ]]; then
        API_URL="https://$API_URL"
    fi
    
    echo -e "${BLUE}Testing deployment:${NC}"
    echo -e "Website: ${GREEN}$WEBSITE_URL${NC}"
    echo -e "API: ${GREEN}$API_URL${NC}"
    echo ""
    
    # Run tests
    run_tests
    test_exit_code=$?
    
    # Ask for load test
    echo ""
    read -p "Run load test? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_load_test
    fi
    
    exit $test_exit_code
}

# Run main function
main "$@"
