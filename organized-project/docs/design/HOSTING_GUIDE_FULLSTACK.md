# 🌐 Complete Full-Stack Hosting Guide: Website + Python Backend

## 🎯 **Overview**
This guide covers hosting both your EDA Easy website AND the Python backend for the de-embedding trial on AWS and HostGator.

---

# 🚀 **OPTION 1: AWS (RECOMMENDED for Professional EDA Business)**

## 📊 **AWS vs HostGator Comparison**

| Feature | AWS | HostGator |
|---------|-----|-----------|
| **Website Hosting** | S3 Static Hosting | Shared Hosting |
| **Python Backend** | Lambda + API Gateway | Limited Python support |
| **Reliability** | 99.99% uptime | 99.9% uptime |
| **Scalability** | Auto-scaling | Fixed resources |
| **Performance** | Global CDN | Single server |
| **Cost** | $5-15/month | $10-25/month |
| **Professional Image** | Enterprise-grade | Shared hosting |
| **Setup Complexity** | Medium | Easy |

**✅ AWS Recommendation: Better for professional EDA business with full-stack requirements.**

---

## 🌐 **PART A: AWS Website Hosting (S3 + CloudFront)**

### **Step 1: Create AWS Account**
1. Go to [aws.amazon.com](https://aws.amazon.com)
2. Click "Create an AWS Account"
3. Enter business email: `<EMAIL>`
4. Account name: `EDA-Easy-Business`
5. Add payment method (free tier available)
6. Verify phone number
7. Choose "Basic Support" (free)

### **Step 2: Set Up S3 Static Website**
1. **Login to AWS Console**
2. **Search for "S3"** → Click S3 service
3. **Create Bucket:**
   - Click "Create bucket"
   - Bucket name: `eda-easy-website` (globally unique)
   - Region: `US East (N. Virginia)` (cheapest)
   - **IMPORTANT:** Uncheck "Block all public access"
   - Check "I acknowledge..." warning
   - Click "Create bucket"

### **Step 3: Upload Website Files**
1. **Click your bucket name**
2. **Upload ALL files:**
   ```
   index.html
   about.html
   services.html
   pricing.html
   contact.html
   de-embedding-trial.html
   css/ (entire folder)
   js/ (entire folder)
   Website-Images2/ (entire folder)
   img/ (entire folder)
   ```
3. **Click "Upload"** and wait for completion

### **Step 4: Enable Static Website Hosting**
1. **Go to "Properties" tab**
2. **Scroll to "Static website hosting"**
3. **Click "Edit"**
4. **Select "Enable"**
5. **Index document:** `index.html`
6. **Error document:** `index.html`
7. **Click "Save changes"**

### **Step 5: Make Website Public**
1. **Go to "Permissions" tab**
2. **Click "Bucket policy" → "Edit"**
3. **Paste this policy** (replace bucket name):
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Sid": "PublicReadGetObject",
         "Effect": "Allow",
         "Principal": "*",
         "Action": "s3:GetObject",
         "Resource": "arn:aws:s3:::eda-easy-website/*"
       }
     ]
   }
   ```
4. **Click "Save changes"**

### **Step 6: Set Up CloudFront CDN (Optional but Recommended)**
1. **Search for "CloudFront"** in AWS Console
2. **Click "Create Distribution"**
3. **Origin Domain:** Select your S3 bucket
4. **Default Root Object:** `index.html`
5. **Price Class:** "Use Only US, Canada and Europe" (cheaper)
6. **Click "Create Distribution"**
7. **Wait 15-20 minutes** for deployment
8. **Your website URL:** `https://d1234567890.cloudfront.net`

---

## 🐍 **PART B: AWS Python Backend (Lambda + API Gateway)**

### **Step 7: Prepare Python Backend**
1. **Create deployment package:**
   ```bash
   mkdir lambda-deployment
   cd lambda-deployment

   # Copy your backend file
   cp /path/to/web_s21_backend.py lambda_function.py

   # Install dependencies
   pip install Flask numpy -t .
   ```

2. **Modify lambda_function.py:**
   ```python
   import json
   import numpy as np
   # ... your existing code ...

   def lambda_handler(event, context):
       # AWS Lambda entry point
       if event['httpMethod'] == 'GET' and event['path'] == '/api/health':
           return {
               'statusCode': 200,
               'headers': {'Content-Type': 'application/json'},
               'body': json.dumps({'status': 'healthy'})
           }
       # ... handle other routes ...
   ```

### **Step 8: Deploy Lambda Function**
1. **Zip the deployment package:**
   ```bash
   zip -r lambda-deployment.zip .
   ```

2. **In AWS Console, search for "Lambda"**
3. **Click "Create function"**
4. **Choose "Author from scratch"**
5. **Function name:** `eda-backend`
6. **Runtime:** `Python 3.9`
7. **Click "Create function"**

8. **Upload your code:**
   - Click "Upload from" → ".zip file"
   - Upload `lambda-deployment.zip`
   - Click "Save"

### **Step 9: Set Up API Gateway**
1. **Search for "API Gateway"**
2. **Click "Create API"**
3. **Choose "REST API" → "Build"**
4. **API name:** `eda-backend-api`
5. **Click "Create API"**

6. **Create resources and methods:**
   - Click "Actions" → "Create Resource"
   - Resource Name: `api`
   - Click "Create Resource"
   - Select `/api` → "Actions" → "Create Method" → "POST"
   - Integration type: "Lambda Function"
   - Lambda Function: `eda-backend`
   - Click "Save"

7. **Deploy API:**
   - Click "Actions" → "Deploy API"
   - Stage: "prod"
   - Click "Deploy"
   - **Copy the Invoke URL:** `https://abc123.execute-api.us-east-1.amazonaws.com/prod`

### **Step 10: Update Frontend to Use Lambda Backend**
1. **Edit your `js/trial.js` file:**
   ```javascript
   // Replace localhost with your API Gateway URL
   const API_BASE_URL = 'https://abc123.execute-api.us-east-1.amazonaws.com/prod';
   ```

2. **Re-upload to S3** with the updated JavaScript file

---

## 💰 **AWS Cost Breakdown**
- **S3 hosting:** $1-3/month
- **CloudFront CDN:** $1-5/month
- **Lambda functions:** $0-5/month (free tier: 1M requests)
- **API Gateway:** $1-10/month
- **Route 53 domain:** $12/year (optional)
- **Total:** $5-15/month

---

# 🏠 **OPTION 2: HostGator (Traditional Shared Hosting)**

## 🌐 **PART A: HostGator Website Hosting**

### **Step 1: Purchase HostGator Hosting**
1. Go to [hostgator.com](https://hostgator.com)
2. Choose "Web Hosting" → "Shared Hosting"
3. Select "Baby Plan" ($5.95/month) or "Business Plan" ($9.95/month)
4. Register domain or use existing one
5. Complete purchase

### **Step 2: Access cPanel**
1. **Login to HostGator Customer Portal**
2. **Click "Manage" next to your hosting account**
3. **Click "cPanel Login"**
4. **You're now in the control panel**

### **Step 3: Upload Website Files**
1. **In cPanel, find "File Manager"**
2. **Navigate to `public_html` folder**
3. **Upload ALL your website files:**
   - Use "Upload" button
   - Select all files and folders
   - Extract if uploaded as ZIP
   - Ensure `index.html` is in root directory

### **Step 4: Set File Permissions**
1. **Select all folders** → Right-click → "Permissions"
2. **Set folders to 755**
3. **Select all files** → Right-click → "Permissions"
4. **Set files to 644**

### **Step 5: Test Website**
- Visit your domain name
- Check all pages load correctly
- Verify images and CSS work

---

## 🐍 **PART B: HostGator Python Backend**

### **Step 6: Enable Python Support**
1. **In cPanel, find "Python Selector"** (if available)
2. **If not available, contact HostGator support:**
   - Request Python 3.x support
   - Ask about Flask application hosting
   - May require upgrade to VPS plan

### **Step 7: Install Python Dependencies**
1. **In cPanel, open "Terminal"** (if available)
2. **Install required packages:**
   ```bash
   pip3 install --user Flask Flask-CORS numpy
   ```

### **Step 8: Deploy Python Backend**
1. **Create `app.py` in your domain root:**
   ```python
   #!/usr/bin/python3
   import sys
   import os

   # Add your project directory to Python path
   sys.path.insert(0, '/home/<USER>/public_html/')

   from web_s21_backend import app

   if __name__ == "__main__":
       app.run()
   ```

2. **Upload your `web_s21_backend.py` file**

### **Step 9: Configure .htaccess**
1. **Create `.htaccess` file in public_html:**
   ```apache
   RewriteEngine On
   RewriteCond %{REQUEST_URI} ^/api/.*$
   RewriteRule ^(.*)$ /app.py/$1 [QSA,L]
   ```

### **Step 10: Test Backend**
- Visit: `yourdomain.com/api/health`
- Should return JSON response

---

## ⚠️ **HostGator Limitations**
- **Limited Python support** on shared hosting
- **May require VPS upgrade** ($30-50/month)
- **Less reliable** than AWS
- **No auto-scaling**
- **Shared resources** affect performance

---

## 🎯 **Final Recommendation**

### **For Professional EDA Business:**
✅ **Use AWS** - More reliable, scalable, professional
- Better for client-facing business
- Handles traffic spikes
- Enterprise-grade infrastructure

### **For Simple Testing:**
⚠️ **HostGator acceptable** - Easier setup but limited
- Good for basic websites
- May struggle with Python backend
- Consider VPS upgrade for full functionality

---

## 🔧 **Next Steps After Deployment**

1. **Test all functionality**
2. **Set up custom domain**
3. **Enable SSL certificate**
4. **Monitor performance**
5. **Set up backups**
6. **Configure analytics**

**Your EDA Easy website with full de-embedding trial functionality will be live! 🚀**

---

## 🔧 **Detailed AWS Lambda Backend Setup**

### **Complete Lambda Function Code (lambda_function.py):**

```python
import json
import numpy as np
import base64
import tempfile
import os
import re

def lambda_handler(event, context):
    """AWS Lambda entry point for EDA backend"""

    # Enable CORS
    headers = {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    }

    try:
        # Handle OPTIONS request (CORS preflight)
        if event['httpMethod'] == 'OPTIONS':
            return {
                'statusCode': 200,
                'headers': headers,
                'body': ''
            }

        # Health check endpoint
        if event['httpMethod'] == 'GET' and event['path'] == '/api/health':
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({'status': 'healthy', 'service': 'EDA Backend'})
            }

        # Demo files endpoint
        if event['httpMethod'] == 'GET' and event['path'] == '/api/demo-files':
            # Return demo S-parameter data
            demo_data = {
                'dut': {
                    'frequencies': list(np.linspace(3.4, 4.4, 100)),
                    's21_db': list(-20 * np.log10(np.linspace(0.1, 1.0, 100))),
                    'filename': 'demo_dut.s2p',
                    'success': True
                },
                'fixture': {
                    'frequencies': list(np.linspace(3.4, 4.4, 100)),
                    's21_db': list(-10 * np.log10(np.linspace(0.8, 1.0, 100))),
                    'filename': 'demo_fixture.s2p',
                    'success': True
                }
            }
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps(demo_data)
            }

        # File upload endpoint
        if event['httpMethod'] == 'POST' and event['path'] == '/api/upload-snp':
            # Handle file upload and processing
            body = json.loads(event['body'])
            file_content = base64.b64decode(body['file_content'])

            # Process S-parameter file
            result = process_snp_content(file_content)

            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps(result)
            }

        # Default response
        return {
            'statusCode': 404,
            'headers': headers,
            'body': json.dumps({'error': 'Endpoint not found'})
        }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': str(e)})
        }

def process_snp_content(file_content):
    """Process S-parameter file content"""
    # Your existing S-parameter processing logic here
    # Return processed data in the same format as your current backend
    pass
```

### **API Gateway CORS Configuration:**

1. **Select your API method**
2. **Click "Actions" → "Enable CORS"**
3. **Set headers:**
   - Access-Control-Allow-Origin: `*`
   - Access-Control-Allow-Headers: `Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token`
   - Access-Control-Allow-Methods: `GET,POST,OPTIONS`
4. **Click "Enable CORS and replace existing CORS headers"**
5. **Deploy API again**

---

## 🏠 **Detailed HostGator Python Setup**

### **Alternative: HostGator VPS Setup**

If shared hosting doesn't support Python:

1. **Upgrade to VPS hosting** ($30-50/month)
2. **Get root access**
3. **Install Python and dependencies:**
   ```bash
   sudo apt update
   sudo apt install python3 python3-pip nginx
   pip3 install Flask Flask-CORS numpy
   ```

4. **Deploy with Gunicorn:**
   ```bash
   pip3 install gunicorn
   gunicorn --bind 0.0.0.0:5000 web_s21_backend:app
   ```

5. **Configure Nginx reverse proxy:**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;

       location /api/ {
           proxy_pass http://127.0.0.1:5000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }

       location / {
           root /var/www/html;
           index index.html;
       }
   }
   ```

---

## 📱 **Frontend Configuration for Both Platforms**

### **Update js/trial.js for AWS:**
```javascript
// AWS Lambda API Gateway URL
const API_BASE_URL = 'https://your-api-id.execute-api.us-east-1.amazonaws.com/prod';

// Update all fetch calls
fetch(`${API_BASE_URL}/api/health`)
    .then(response => response.json())
    .then(data => console.log(data));
```

### **Update js/trial.js for HostGator:**
```javascript
// HostGator domain URL
const API_BASE_URL = 'https://yourdomain.com';

// Same fetch calls work
fetch(`${API_BASE_URL}/api/health`)
    .then(response => response.json())
    .then(data => console.log(data));
```

---

## 🔒 **Security & SSL Setup**

### **AWS SSL (Free with CloudFront):**
1. **In CloudFront distribution settings**
2. **Request SSL certificate** from AWS Certificate Manager
3. **Automatic HTTPS** enabled

### **HostGator SSL:**
1. **In cPanel, find "SSL/TLS"**
2. **Purchase SSL certificate** or use Let's Encrypt (free)
3. **Install certificate**
4. **Force HTTPS redirect**

---

## 📊 **Performance Monitoring**

### **AWS CloudWatch:**
- Monitor Lambda function performance
- Track API Gateway requests
- Set up alerts for errors

### **HostGator:**
- Use cPanel metrics
- Monitor resource usage
- Set up uptime monitoring

---

## 💾 **Backup Strategies**

### **AWS:**
- S3 versioning enabled
- Lambda function code in version control
- Automated CloudFormation templates

### **HostGator:**
- cPanel backup tools
- Manual file downloads
- Database exports (if using databases)

---

## 🚀 **Go-Live Checklist**

- [ ] Website loads on all pages
- [ ] De-embedding trial works
- [ ] File uploads function
- [ ] Demo files load correctly
- [ ] Mobile responsive design
- [ ] SSL certificate active
- [ ] Domain pointing correctly
- [ ] Contact forms working
- [ ] Analytics tracking setup
- [ ] Backup system configured

**Your professional EDA Easy website is ready for business! 🎯**
