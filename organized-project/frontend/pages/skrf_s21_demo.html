<!DOCTYPE html>
<html>
<head>
    <title>S21 Demo with scikit-rf Backend</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .plot-container { 
            width: 100%; 
            height: 500px; 
            margin: 20px 0; 
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button { 
            padding: 12px 24px; 
            margin: 10px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .status { 
            margin: 10px 0; 
            padding: 15px; 
            background: #e9ecef; 
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .upload-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            text-align: center;
        }
        .file-input {
            margin: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #555; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 S21 Plotting with scikit-rf</h1>
        
        <h2>Demo Files (Using scikit-rf backend)</h2>
        <button onclick="loadDemoFiles()" id="demoBtn">Load Demo SNP Files</button>
        <div id="status" class="status">Ready to load demo files using scikit-rf...</div>
        
        <h2>Upload Your Own Files</h2>
        <div class="upload-section">
            <div>
                <label>DUT File (Fixture-DUT-Fixture):</label><br>
                <input type="file" id="dutFile" class="file-input" accept=".s2p,.snp" onchange="uploadFile('dut')">
            </div>
            <div>
                <label>Fixture File (Fixture-Fixture):</label><br>
                <input type="file" id="fixtureFile" class="file-input" accept=".s2p,.snp" onchange="uploadFile('fixture')">
            </div>
            <button onclick="plotUploadedFiles()" id="plotBtn" disabled>Plot Uploaded Files</button>
        </div>
        
        <div id="plot" class="plot-container"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let uploadedData = { dut: null, fixture: null };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = message;
            status.className = `status ${type}`;
        }
        
        async function loadDemoFiles() {
            const btn = document.getElementById('demoBtn');
            btn.disabled = true;
            btn.innerHTML = 'Loading...';
            
            try {
                updateStatus('🔄 Loading demo files with scikit-rf...', 'info');
                
                const response = await fetch(`${API_BASE}/demo-files`);
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to load demo files');
                }
                
                updateStatus('✅ Demo files loaded successfully!', 'success');
                
                // Plot the data
                plotS21Data(data.dut_data, data.fixture_data);
                
            } catch (error) {
                console.error('Error:', error);
                updateStatus(`❌ Error: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.innerHTML = 'Load Demo SNP Files';
            }
        }
        
        async function uploadFile(type) {
            const fileInput = document.getElementById(type === 'dut' ? 'dutFile' : 'fixtureFile');
            const file = fileInput.files[0];
            
            if (!file) return;
            
            try {
                updateStatus(`🔄 Uploading ${type} file...`, 'info');
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('type', type);
                
                const response = await fetch(`${API_BASE}/upload-snp`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to process file');
                }
                
                uploadedData[type] = data;
                updateStatus(`✅ ${type.toUpperCase()} file processed: ${data.num_points} points`, 'success');
                
                // Enable plot button if both files are uploaded
                if (uploadedData.dut && uploadedData.fixture) {
                    document.getElementById('plotBtn').disabled = false;
                }
                
            } catch (error) {
                console.error('Error:', error);
                updateStatus(`❌ Error uploading ${type} file: ${error.message}`, 'error');
            }
        }
        
        function plotUploadedFiles() {
            if (!uploadedData.dut || !uploadedData.fixture) {
                updateStatus('❌ Please upload both DUT and fixture files', 'error');
                return;
            }
            
            plotS21Data(uploadedData.dut, uploadedData.fixture);
        }
        
        function plotS21Data(dutData, fixtureData) {
            try {
                updateStatus('🎨 Creating S21 plot...', 'info');
                
                // Create traces
                const trace1 = {
                    x: dutData.frequencies,
                    y: dutData.s21_db,
                    type: 'scatter',
                    mode: 'lines',
                    name: `Fixture-DUT-Fixture (${dutData.filename})`,
                    line: { color: '#dc3545', width: 2 }
                };
                
                const trace2 = {
                    x: fixtureData.frequencies,
                    y: fixtureData.s21_db,
                    type: 'scatter',
                    mode: 'lines',
                    name: `Fixture-Fixture (${fixtureData.filename})`,
                    line: { color: '#28a745', width: 2 }
                };
                
                // Create layout
                const layout = {
                    title: {
                        text: 'S21 Insertion Loss Comparison (scikit-rf)',
                        font: { size: 18 }
                    },
                    xaxis: {
                        title: `Frequency (${dutData.freq_unit})`,
                        type: 'linear',
                        gridcolor: '#e9ecef'
                    },
                    yaxis: {
                        title: 'S21 (dB)',
                        gridcolor: '#e9ecef'
                    },
                    legend: {
                        x: 0.02,
                        y: 0.98,
                        bgcolor: 'rgba(255,255,255,0.8)',
                        bordercolor: '#ddd',
                        borderwidth: 1
                    },
                    plot_bgcolor: '#fafafa',
                    margin: { t: 60, r: 50, b: 60, l: 70 }
                };
                
                // Plot
                Plotly.newPlot('plot', [trace1, trace2], layout, {
                    responsive: true,
                    displayModeBar: true
                });
                
                updateStatus(`✅ Plot created! DUT: ${dutData.num_points} points, Fixture: ${fixtureData.num_points} points`, 'success');
                
            } catch (error) {
                console.error('Plotting error:', error);
                updateStatus(`❌ Plotting error: ${error.message}`, 'error');
            }
        }
        
        // Check if server is running
        async function checkServer() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                updateStatus(`🟢 Server ready (scikit-rf ${data.skrf_version})`, 'success');
            } catch (error) {
                updateStatus('🔴 Server not running. Please start the Python backend first.', 'error');
            }
        }
        
        // Check server status on load
        window.onload = checkServer;
    </script>
</body>
</html>
