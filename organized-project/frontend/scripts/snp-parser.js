/**
 * SNP File Parser for S-parameter data
 */

class SNPParser {
    constructor() {
        this.data = null;
        this.frequencies = [];
        this.sParameters = {};
        this.numPorts = 0;
        this.format = 'MA'; // Default: Magnitude-Angle
        this.unit = 'GHz';
        this.impedance = 50;
    }

    /**
     * Parse SNP file content
     * @param {string} content - File content as string
     * @returns {Object} Parsed S-parameter data
     */
    parse(content) {
        console.log('Parsing SNP file...');
        const lines = content.split('\n').map(line => line.trim());
        let dataLines = [];

        // Parse header and extract metadata
        for (let line of lines) {
            if (line.startsWith('!') || line.length === 0) {
                continue; // Skip comments and empty lines
            }

            if (line.startsWith('#')) {
                this.parseHeader(line);
                console.log(`Header parsed: ${this.unit} ${this.format} R${this.impedance}`);
                continue;
            }

            dataLines.push(line);
        }

        console.log(`Found ${dataLines.length} data lines`);

        // Parse data lines
        this.parseData(dataLines);

        console.log(`Parsed ${this.frequencies.length} frequency points`);
        console.log(`S-parameters available: ${Object.keys(this.sParameters)}`);

        return {
            frequencies: this.frequencies,
            sParameters: this.sParameters,
            numPorts: this.numPorts,
            format: this.format,
            unit: this.unit,
            impedance: this.impedance
        };
    }

    /**
     * Parse header line (starts with #)
     * @param {string} line - Header line
     */
    parseHeader(line) {
        const parts = line.substring(1).trim().split(/\s+/);

        if (parts.length >= 4) {
            this.unit = parts[0].toUpperCase();
            this.format = parts[1].toUpperCase();
            this.impedance = parseFloat(parts[3]) || 50;
        }
    }

    /**
     * Parse data lines
     * @param {Array} lines - Array of data lines
     */
    parseData(lines) {
        if (lines.length === 0) return;

        // Determine number of ports from first data line
        const firstLine = lines[0].split(/\s+/).filter(val => val.length > 0);
        this.numPorts = this.determineNumPorts(firstLine.length);

        const expectedParams = this.numPorts * this.numPorts * 2; // Real and imaginary parts

        for (let line of lines) {
            const values = line.split(/\s+/).filter(val => val.length > 0).map(parseFloat);

            if (values.length < 3) continue; // Skip invalid lines

            const frequency = values[0];
            this.frequencies.push(frequency);

            // Parse S-parameters
            const sparams = values.slice(1);
            this.parseSParameters(frequency, sparams);
        }
    }

    /**
     * Determine number of ports from data line length
     * @param {number} dataLength - Length of data values in line
     * @returns {number} Number of ports
     */
    determineNumPorts(dataLength) {
        // dataLength = 1 (freq) + numPorts^2 * 2 (real/imag or mag/phase)
        const paramCount = dataLength - 1;
        const numPorts = Math.sqrt(paramCount / 2);
        return Math.round(numPorts);
    }

    /**
     * Parse S-parameters from data values
     * @param {number} frequency - Frequency point
     * @param {Array} values - S-parameter values
     */
    parseSParameters(frequency, values) {
        let index = 0;

        for (let i = 1; i <= this.numPorts; i++) {
            for (let j = 1; j <= this.numPorts; j++) {
                const paramName = `S${i}${j}`;

                if (!this.sParameters[paramName]) {
                    this.sParameters[paramName] = {
                        frequencies: [],
                        real: [],
                        imag: [],
                        magnitude: [],
                        phase: []
                    };
                }

                if (index + 1 < values.length) {
                    const val1 = values[index];
                    const val2 = values[index + 1];

                    if (this.format === 'MA') {
                        // Magnitude and Angle format
                        const magnitude = val1;
                        const phase = val2; // in degrees
                        const phaseRad = phase * Math.PI / 180;

                        const real = magnitude * Math.cos(phaseRad);
                        const imag = magnitude * Math.sin(phaseRad);

                        this.sParameters[paramName].frequencies.push(frequency);
                        this.sParameters[paramName].magnitude.push(magnitude);
                        this.sParameters[paramName].phase.push(phase);
                        this.sParameters[paramName].real.push(real);
                        this.sParameters[paramName].imag.push(imag);

                    } else if (this.format === 'RI') {
                        // Real and Imaginary format
                        const real = val1;
                        const imag = val2;

                        const magnitude = Math.sqrt(real * real + imag * imag);
                        const phase = Math.atan2(imag, real) * 180 / Math.PI;

                        this.sParameters[paramName].frequencies.push(frequency);
                        this.sParameters[paramName].real.push(real);
                        this.sParameters[paramName].imag.push(imag);
                        this.sParameters[paramName].magnitude.push(magnitude);
                        this.sParameters[paramName].phase.push(phase);
                    }

                    index += 2;
                }
            }
        }
    }

    /**
     * Get S21 insertion loss in dB
     * @returns {Object} Frequencies and S21 in dB
     */
    getS21_dB() {
        if (!this.sParameters.S21) {
            console.log('No S21 data found');
            return { frequencies: [], s21_dB: [] };
        }

        console.log(`S21 data points: ${this.sParameters.S21.magnitude.length}`);
        console.log(`Sample S21 magnitude: ${this.sParameters.S21.magnitude[0]}`);

        // Convert magnitude to dB: 20*log10(magnitude)
        const s21_dB = this.sParameters.S21.magnitude.map(mag => {
            const dB = 20 * Math.log10(Math.abs(mag));
            return isFinite(dB) ? dB : -100; // Handle log(0) case
        });

        console.log(`Sample S21 dB: ${s21_dB[0]}`);

        return {
            frequencies: this.sParameters.S21.frequencies,
            s21_dB: s21_dB
        };
    }

    /**
     * Get S-parameter as complex numbers
     * @param {string} param - Parameter name (e.g., 'S21')
     * @returns {Array} Array of complex numbers
     */
    getSParameterComplex(param) {
        if (!this.sParameters[param]) {
            return [];
        }

        const real = this.sParameters[param].real;
        const imag = this.sParameters[param].imag;

        return real.map((r, i) => ({ real: r, imag: imag[i] }));
    }

    /**
     * Convert frequency to specified unit
     * @param {number} freq - Frequency value
     * @param {string} targetUnit - Target unit (Hz, kHz, MHz, GHz)
     * @returns {number} Converted frequency
     */
    convertFrequency(freq, targetUnit = 'GHz') {
        const units = {
            'HZ': 1,
            'KHZ': 1e3,
            'MHZ': 1e6,
            'GHZ': 1e9
        };

        const fromUnit = units[this.unit.toUpperCase()] || 1;
        const toUnit = units[targetUnit.toUpperCase()] || 1;

        return freq * fromUnit / toUnit;
    }

    /**
     * Get frequency range info
     * @returns {Object} Min, max, and count of frequency points
     */
    getFrequencyInfo() {
        if (this.frequencies.length === 0) {
            return { min: 0, max: 0, count: 0, unit: this.unit };
        }

        return {
            min: Math.min(...this.frequencies),
            max: Math.max(...this.frequencies),
            count: this.frequencies.length,
            unit: this.unit
        };
    }
}

/**
 * De-embedding Calculator
 */
class DeEmbedding {
    constructor() {
        this.fixtureData = null;
        this.dutFixtureData = null;
        this.deEmbeddedData = null;
    }

    /**
     * Perform de-embedding calculation
     * @param {Object} dutFixtureData - Fixture-DUT-Fixture S-parameters
     * @param {Object} fixtureData - Fixture-Fixture S-parameters
     * @returns {Object} De-embedded DUT S-parameters
     */
    deEmbed(dutFixtureData, fixtureData) {
        this.dutFixtureData = dutFixtureData;
        this.fixtureData = fixtureData;

        // Find common frequency points
        const commonFreqs = this.findCommonFrequencies();

        // Perform de-embedding for each frequency point
        const deEmbeddedSParams = {
            frequencies: commonFreqs,
            S11: { frequencies: commonFreqs, real: [], imag: [], magnitude: [], phase: [] },
            S12: { frequencies: commonFreqs, real: [], imag: [], magnitude: [], phase: [] },
            S21: { frequencies: commonFreqs, real: [], imag: [], magnitude: [], phase: [] },
            S22: { frequencies: commonFreqs, real: [], imag: [], magnitude: [], phase: [] }
        };

        for (let freq of commonFreqs) {
            const dutFixtureMatrix = this.getSMatrixAtFreq(dutFixtureData, freq);
            const fixtureMatrix = this.getSMatrixAtFreq(fixtureData, freq);

            // Simple de-embedding: S_DUT = S_fixture^-1 * S_dutFixture * S_fixture^-1
            const deEmbeddedMatrix = this.performDeEmbedding(dutFixtureMatrix, fixtureMatrix);

            // Store results
            this.storeSParameterResults(deEmbeddedSParams, deEmbeddedMatrix, freq);
        }

        this.deEmbeddedData = {
            frequencies: commonFreqs,
            sParameters: deEmbeddedSParams,
            numPorts: 2,
            format: 'RI',
            unit: dutFixtureData.unit,
            impedance: dutFixtureData.impedance
        };

        return this.deEmbeddedData;
    }

    /**
     * Find common frequency points between datasets
     * @returns {Array} Common frequencies
     */
    findCommonFrequencies() {
        const freq1 = this.dutFixtureData.frequencies;
        const freq2 = this.fixtureData.frequencies;

        // Find intersection with tolerance
        const tolerance = 1e-6;
        const common = [];

        for (let f1 of freq1) {
            for (let f2 of freq2) {
                if (Math.abs(f1 - f2) < tolerance) {
                    common.push(f1);
                    break;
                }
            }
        }

        return common.sort((a, b) => a - b);
    }

    /**
     * Get S-parameter matrix at specific frequency
     * @param {Object} data - S-parameter data
     * @param {number} freq - Frequency point
     * @returns {Array} 2x2 S-parameter matrix
     */
    getSMatrixAtFreq(data, freq) {
        const tolerance = 1e-6;
        let index = -1;

        // Find frequency index
        for (let i = 0; i < data.frequencies.length; i++) {
            if (Math.abs(data.frequencies[i] - freq) < tolerance) {
                index = i;
                break;
            }
        }

        if (index === -1) {
            throw new Error(`Frequency ${freq} not found in data`);
        }

        // Extract S-parameters at this frequency
        const s11 = {
            real: data.sParameters.S11.real[index],
            imag: data.sParameters.S11.imag[index]
        };
        const s12 = {
            real: data.sParameters.S12.real[index],
            imag: data.sParameters.S12.imag[index]
        };
        const s21 = {
            real: data.sParameters.S21.real[index],
            imag: data.sParameters.S21.imag[index]
        };
        const s22 = {
            real: data.sParameters.S22.real[index],
            imag: data.sParameters.S22.imag[index]
        };

        return [[s11, s12], [s21, s22]];
    }

    /**
     * Perform de-embedding calculation (simplified method)
     * @param {Array} dutFixtureMatrix - DUT+Fixture S-matrix
     * @param {Array} fixtureMatrix - Fixture S-matrix
     * @returns {Array} De-embedded DUT S-matrix
     */
    performDeEmbedding(dutFixtureMatrix, fixtureMatrix) {
        // Simplified de-embedding: assume symmetric fixtures
        // S_DUT ≈ S_dutFixture / S_fixture (element-wise for S21)

        const s11_dut = this.complexDivide(dutFixtureMatrix[0][0], fixtureMatrix[0][0]);
        const s12_dut = this.complexDivide(dutFixtureMatrix[0][1], fixtureMatrix[0][1]);
        const s21_dut = this.complexDivide(dutFixtureMatrix[1][0], fixtureMatrix[1][0]);
        const s22_dut = this.complexDivide(dutFixtureMatrix[1][1], fixtureMatrix[1][1]);

        return [[s11_dut, s12_dut], [s21_dut, s22_dut]];
    }

    /**
     * Complex number division
     * @param {Object} a - Complex number {real, imag}
     * @param {Object} b - Complex number {real, imag}
     * @returns {Object} Result of a/b
     */
    complexDivide(a, b) {
        const denominator = b.real * b.real + b.imag * b.imag;

        if (Math.abs(denominator) < 1e-15) {
            return { real: 0, imag: 0 };
        }

        return {
            real: (a.real * b.real + a.imag * b.imag) / denominator,
            imag: (a.imag * b.real - a.real * b.imag) / denominator
        };
    }

    /**
     * Store S-parameter results
     * @param {Object} results - Results object to store in
     * @param {Array} matrix - S-parameter matrix
     * @param {number} freq - Frequency
     */
    storeSParameterResults(results, matrix, freq) {
        const params = ['S11', 'S12', 'S21', 'S22'];
        const positions = [[0,0], [0,1], [1,0], [1,1]];

        for (let i = 0; i < params.length; i++) {
            const [row, col] = positions[i];
            const complex = matrix[row][col];

            const magnitude = Math.sqrt(complex.real * complex.real + complex.imag * complex.imag);
            const phase = Math.atan2(complex.imag, complex.real) * 180 / Math.PI;

            results[params[i]].real.push(complex.real);
            results[params[i]].imag.push(complex.imag);
            results[params[i]].magnitude.push(magnitude);
            results[params[i]].phase.push(phase);
        }
    }

    /**
     * Get de-embedded S21 in dB
     * @returns {Object} Frequencies and S21 in dB
     */
    getDeEmbeddedS21_dB() {
        if (!this.deEmbeddedData || !this.deEmbeddedData.sParameters.S21) {
            return { frequencies: [], s21_dB: [] };
        }

        const s21_dB = this.deEmbeddedData.sParameters.S21.magnitude.map(mag =>
            20 * Math.log10(Math.abs(mag))
        );

        return {
            frequencies: this.deEmbeddedData.frequencies,
            s21_dB: s21_dB
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SNPParser, DeEmbedding };
} else {
    window.SNPParser = SNPParser;
    window.DeEmbedding = DeEmbedding;
}
