#!/bin/bash

# 🔄 Sync Organized Project to AWS

set -e

echo "🔄 Syncing organized project to AWS..."

# Create temporary deployment directory
mkdir -p temp-deploy
cd temp-deploy

# Copy frontend files
cp -r ../frontend/pages/* ./
cp -r ../frontend/styles ./css
cp -r ../frontend/scripts ./js
cp -r ../frontend/images ./Website-Images2

# Sync to AWS
aws s3 sync . s3://$(aws s3 ls | grep "eda-easy-website" | awk '{print $3}' | head -1) \
    --exclude "*.sh" \
    --exclude "*.py" \
    --exclude "*.md" \
    --delete

# Cleanup
cd ..
rm -rf temp-deploy

echo "✅ Organized project synced to AWS!"
