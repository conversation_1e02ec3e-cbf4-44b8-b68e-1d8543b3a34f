<!DOCTYPE html>
<html>
<head>
    <title>Test SNP Files</title>
</head>
<body>
    <h1>SNP Files Test</h1>
    <button onclick="testFiles()">Test SNP File Loading</button>
    <div id="results"></div>

    <script>
        async function testFiles() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Testing SNP file loading...</p>';
            
            try {
                // Test loading the files
                console.log('Testing file paths...');
                
                const dutResponse = await fetch('./snpfiles/SfFilterSb.s2p');
                const fixtureResponse = await fetch('./snpfiles/SfSb.s2p');
                
                console.log('DUT response:', dutResponse.status, dutResponse.statusText);
                console.log('Fixture response:', fixtureResponse.status, fixtureResponse.statusText);
                
                if (dutResponse.ok && fixtureResponse.ok) {
                    const dutText = await dutResponse.text();
                    const fixtureText = await fixtureResponse.text();
                    
                    results.innerHTML = `
                        <h2>✅ Files loaded successfully!</h2>
                        <p><strong>DUT file (SfFilterSb.s2p):</strong> ${dutText.length} characters</p>
                        <p><strong>Fixture file (SfSb.s2p):</strong> ${fixtureText.length} characters</p>
                        <h3>DUT File Preview:</h3>
                        <pre style="background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;">${dutText.substring(0, 500)}...</pre>
                        <h3>Fixture File Preview:</h3>
                        <pre style="background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;">${fixtureText.substring(0, 500)}...</pre>
                    `;
                } else {
                    results.innerHTML = `
                        <h2>❌ Failed to load files</h2>
                        <p>DUT file status: ${dutResponse.status}</p>
                        <p>Fixture file status: ${fixtureResponse.status}</p>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                results.innerHTML = `<h2>❌ Error: ${error.message}</h2>`;
            }
        }
    </script>
</body>
</html>
