#!/usr/bin/env python3
"""
Direct S21 plotting using scikit-rf - generates actual plots
"""

import skrf as rf
import matplotlib.pyplot as plt
import numpy as np
import os

def plot_s21_comparison():
    """
    Load SNP files and create S21 comparison plot
    """
    try:
        # File paths
        dut_file = 'snpfiles/SfFilterSb.s2p'
        fixture_file = 'snpfiles/SfSb.s2p'
        
        print("🎯 Loading SNP files with scikit-rf...")
        
        # Check if files exist
        if not os.path.exists(dut_file):
            print(f"❌ DUT file not found: {dut_file}")
            return
        
        if not os.path.exists(fixture_file):
            print(f"❌ Fixture file not found: {fixture_file}")
            return
        
        # Load networks using scikit-rf
        print(f"📁 Loading {dut_file}...")
        dut_network = rf.Network(dut_file)
        
        print(f"📁 Loading {fixture_file}...")
        fixture_network = rf.Network(fixture_file)
        
        print(f"✅ Files loaded successfully!")
        print(f"   DUT: {len(dut_network.f)} frequency points")
        print(f"   Fixture: {len(fixture_network.f)} frequency points")
        
        # Extract S21 using s[:,1,0] as you specified
        print("📊 Extracting S21 parameters...")
        
        # S21 = s[:,1,0] (output port 2, input port 1)
        dut_s21 = dut_network.s[:, 1, 0]
        fixture_s21 = fixture_network.s[:, 1, 0]
        
        # Convert to dB
        dut_s21_db = 20 * np.log10(np.abs(dut_s21))
        fixture_s21_db = 20 * np.log10(np.abs(fixture_s21))
        
        # Get frequencies
        dut_freq = dut_network.f
        fixture_freq = fixture_network.f
        
        # Determine appropriate frequency unit
        max_freq = max(np.max(dut_freq), np.max(fixture_freq))
        if max_freq >= 1e9:
            freq_scale = 1e9
            freq_unit = 'GHz'
        elif max_freq >= 1e6:
            freq_scale = 1e6
            freq_unit = 'MHz'
        elif max_freq >= 1e3:
            freq_scale = 1e3
            freq_unit = 'kHz'
        else:
            freq_scale = 1
            freq_unit = 'Hz'
        
        # Scale frequencies
        dut_freq_scaled = dut_freq / freq_scale
        fixture_freq_scaled = fixture_freq / freq_scale
        
        print(f"📈 Creating plot with frequency in {freq_unit}...")
        
        # Create the plot
        plt.figure(figsize=(12, 8))
        
        # Plot S21 data
        plt.plot(dut_freq_scaled, dut_s21_db, 'r-', linewidth=2, 
                label='Fixture-DUT-Fixture (SfFilterSb.s2p)')
        plt.plot(fixture_freq_scaled, fixture_s21_db, 'g-', linewidth=2, 
                label='Fixture-Fixture (SfSb.s2p)')
        
        # Customize plot
        plt.xlabel(f'Frequency ({freq_unit})', fontsize=12)
        plt.ylabel('S21 (dB)', fontsize=12)
        plt.title('S21 Insertion Loss Comparison\n(Generated with scikit-rf)', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend(fontsize=11)
        
        # Add some statistics
        dut_min = np.min(dut_s21_db)
        dut_max = np.max(dut_s21_db)
        fixture_min = np.min(fixture_s21_db)
        fixture_max = np.max(fixture_s21_db)
        
        stats_text = f"""Statistics:
DUT S21: {dut_min:.2f} to {dut_max:.2f} dB
Fixture S21: {fixture_min:.2f} to {fixture_max:.2f} dB
Freq Range: {np.min(dut_freq_scaled):.2f} to {np.max(dut_freq_scaled):.2f} {freq_unit}"""
        
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
                fontsize=9, verticalalignment='top', 
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        # Save the plot
        output_file = 's21_comparison_plot.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"💾 Plot saved as: {output_file}")
        
        # Show the plot
        plt.show()
        
        print("✅ S21 plotting completed successfully!")
        
        # Print some sample data
        print(f"\n📋 Sample data:")
        print(f"   DUT S21 at first frequency: {dut_s21_db[0]:.2f} dB")
        print(f"   Fixture S21 at first frequency: {fixture_s21_db[0]:.2f} dB")
        print(f"   Frequency range: {np.min(dut_freq_scaled):.2f} - {np.max(dut_freq_scaled):.2f} {freq_unit}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """
    Main function
    """
    print("🎯 S21 Plotting with scikit-rf")
    print("=" * 40)
    
    # Check scikit-rf version
    try:
        print(f"📦 scikit-rf version: {rf.__version__}")
    except:
        print("📦 scikit-rf version: unknown")
    
    # Create the plot
    plot_s21_comparison()

if __name__ == "__main__":
    main()
