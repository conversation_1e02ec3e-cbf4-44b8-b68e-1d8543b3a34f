#!/usr/bin/env python3
"""
S21 plotting using the same approach as rf_process7.py
"""

import matplotlib.pyplot as plt
import numpy as np
import os
import ntwk_1  # Using the same network module as rf_process7.py

def parse_s2p_file(filename):
    """
    Parse S2P file manually to extract S21 data
    """
    print(f"📁 Parsing {filename}...")

    frequencies = []
    s21_magnitude = []
    s21_phase = []

    with open(filename, 'r') as f:
        lines = f.readlines()

    freq_unit = 'Hz'

    for line in lines:
        line = line.strip()

        # Skip comments and empty lines
        if line.startswith('!') or len(line) == 0:
            continue

        # Parse header line
        if line.startswith('#'):
            # Format: # Hz S MA R 50
            parts = line[1:].strip().split()
            if len(parts) >= 1:
                freq_unit = parts[0]
            print(f"   Header: {line}")
            continue

        # Parse data line
        # Format: freq S11_mag S11_phase S21_mag S21_phase S12_mag S12_phase S22_mag S22_phase
        # Based on your data: 3400000000 0.97916502 -153.08113 4.3408869e-005 -97.922905 5.3289001e-005 -70.954994 0.97903419 -138.29231
        # Index:              0          1          2           3             4          5             6          7          8
        values = line.split()
        if len(values) >= 9:
            freq = float(values[0])      # Frequency
            s21_mag = float(values[3])   # S21 magnitude at index 3
            s21_ph = float(values[4])    # S21 phase at index 4

            frequencies.append(freq)
            s21_magnitude.append(s21_mag)
            s21_phase.append(s21_ph)

    print(f"   Parsed {len(frequencies)} frequency points")
    print(f"   Frequency unit: {freq_unit}")
    if s21_magnitude:
        print(f"   Sample S21 magnitude: {s21_magnitude[0]}")
        print(f"   Frequency range: {frequencies[0]:.0f} - {frequencies[-1]:.0f} {freq_unit}")
        print(f"   S21 magnitude range: {min(s21_magnitude):.6f} - {max(s21_magnitude):.6f}")
    else:
        print("   No S21 data found!")

    return {
        'frequencies': np.array(frequencies),
        's21_magnitude': np.array(s21_magnitude),
        's21_phase': np.array(s21_phase),
        'freq_unit': freq_unit
    }

def plot_s21_comparison():
    """
    Load S2P files and create S21 comparison plot using the same approach as rf_process7.py
    """
    try:
        # File paths
        dut_file = 'snpfiles/CW_28AWG2Drain_gen.s2p' # 'snpfiles/SfFilterSb.s2p'
        fixture_file = 'snpfiles/SfFilterSb.s2p' # 'snpfiles/SfSb.s2p'

        dut_file = 'snpfiles/SfFilterSb.s2p'
        fixture_file = 'snpfiles/SfSb.s2p'

        print("🎯 Loading S2P files using ntwk_1.Network...")

        # Check if files exist
        if not os.path.exists(dut_file):
            print(f"❌ DUT file not found: {dut_file}")
            return

        if not os.path.exists(fixture_file):
            print(f"❌ Fixture file not found: {fixture_file}")
            return

        # Load networks using the same method as rf_process7.py
        print(f"📁 Loading {dut_file}...")
        dut_ntwk = ntwk_1.Network(dut_file)

        print(f"📁 Loading {fixture_file}...")
        fixture_ntwk = ntwk_1.Network(fixture_file)

        print("✅ Files loaded successfully!")
        print(f"   DUT: {len(dut_ntwk.freq)} frequency points")
        print(f"   Fixture: {len(fixture_ntwk.freq)} frequency points")

        # Extract S21 data using the same approach as rf_process7.py
        print("📊 Extracting S21 insertion loss...")

        # Get frequency in GHz (same as rf_process7.py line 297)
        dut_freq = dut_ntwk.freq / 1e9  # Convert to GHz
        fixture_freq = fixture_ntwk.freq / 1e9  # Convert to GHz

        # Get S21 in dB (same as rf_process7.py line 298)
        # For 2-port: S21 is s_db[:, 1, 0] (output port 2, input port 1)
        dut_s21_db = dut_ntwk.s_db[:, 1, 0]  # S21 in dB
        fixture_s21_db = fixture_ntwk.s_db[:, 1, 0]  # S21 in dB

        print(f"   DUT S21 dB range: {np.min(dut_s21_db):.2f} to {np.max(dut_s21_db):.2f} dB")
        print(f"   Fixture S21 dB range: {np.min(fixture_s21_db):.2f} to {np.max(fixture_s21_db):.2f} dB")
        print(f"   Frequency range: {np.min(dut_freq):.2f} to {np.max(dut_freq):.2f} GHz")

        # Create the plot (same style as rf_process7.py)
        print("📈 Creating S21 insertion loss plot...")

        fig, axs = plt.subplots(figsize=(12, 8))

        # Plot S21 data (same as rf_process7.py line 299)
        axs.plot(dut_freq, dut_s21_db, 'r-', linewidth=2,
                label='Fixture-DUT-Fixture (SfFilterSb.s2p)')
        axs.plot(fixture_freq, fixture_s21_db, 'g-', linewidth=2,
                label='Fixture-Fixture (SfSb.s2p)')

        # Customize plot (same style as rf_process7.py)
        axs.set_title('S21 Insertion Loss Comparison', fontsize=16, weight='bold')
        axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
        axs.set_ylabel('S21 (dB)', fontsize=14, weight='bold')
        axs.legend()
        axs.grid(True)

        # Add statistics
        dut_min = np.min(dut_s21_db)
        dut_max = np.max(dut_s21_db)
        fixture_min = np.min(fixture_s21_db)
        fixture_max = np.max(fixture_s21_db)

        stats_text = f"""Statistics:
DUT S21: {dut_min:.2f} to {dut_max:.2f} dB
Fixture S21: {fixture_min:.2f} to {fixture_max:.2f} dB
Freq Range: {np.min(dut_freq):.2f} to {np.max(dut_freq):.2f} GHz
Data Points: DUT={len(dut_s21_db)}, Fixture={len(fixture_s21_db)}"""

        axs.text(0.02, 0.98, stats_text, transform=axs.transAxes,
                fontsize=9, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()

        # Save the plot
        output_file = 's21_rf_process_style.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"💾 Plot saved as: {output_file}")

        # Show the plot
        plt.show()

        print("✅ S21 plotting completed successfully!")

        # Print sample data
        print(f"\n📋 Sample data:")
        print(f"   DUT S21 at first frequency: {dut_s21_db[0]:.2f} dB")
        print(f"   Fixture S21 at first frequency: {fixture_s21_db[0]:.2f} dB")
        print(f"   Frequency range: {np.min(dut_freq):.2f} - {np.max(dut_freq):.2f} GHz")

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """
    Main function
    """
    print("🎯 S21 Plotting using rf_process7.py approach")
    print("=" * 50)

    # Check if ntwk_1 module is available
    try:
        import ntwk_1
        print(f"✅ ntwk_1 module found")
    except ImportError:
        print("❌ ntwk_1 module not found. Please ensure it's in the same directory.")
        return

    # Create the plot
    plot_s21_comparison()

if __name__ == "__main__":
    main()
