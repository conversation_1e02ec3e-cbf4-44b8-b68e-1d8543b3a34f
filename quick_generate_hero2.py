#!/usr/bin/env python3
"""
Quick generator for Services-Hero2.jpg with custom prompt
"""

import os
import requests
from pathlib import Path

def generate_services_hero2(custom_prompt):
    """Generate Services-Hero2.jpg with custom prompt"""
    
    # Check API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OpenAI API key not found!")
        print("Please set: export OPENAI_API_KEY='your-key-here'")
        return False
    
    print(f"🎨 Generating Services-Hero2.jpg")
    print(f"📝 Prompt: {custom_prompt}")
    print(f"📏 Size: 1792x1024")
    print(f"💰 Cost: ~$0.04")
    
    # Prepare API request
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        "model": "dall-e-3",
        "prompt": custom_prompt,
        "n": 1,
        "size": "1792x1024",
        "quality": "standard"
    }
    
    try:
        print("📤 Making API request...")
        response = requests.post(
            'https://api.openai.com/v1/images/generations',
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            image_url = result['data'][0]['url']
            
            print("📥 Downloading image...")
            img_response = requests.get(image_url, timeout=60)
            
            if img_response.status_code == 200:
                # Create directory if needed
                output_dir = Path("Website-Images2/Services")
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # Save image
                output_path = output_dir / "Services-Hero2.jpg"
                with open(output_path, 'wb') as f:
                    f.write(img_response.content)
                
                print(f"✅ Services-Hero2.jpg saved successfully!")
                print(f"📁 Location: {output_path}")
                return True
            else:
                print(f"❌ Failed to download image: {img_response.status_code}")
                return False
        else:
            print(f"❌ API Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"📝 Details: {error_data}")
            except:
                print(f"📝 Raw error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Main function"""
    print("⚡ Quick Services-Hero2.jpg Generator")
    print("=" * 40)
    
    # Get prompt from user
    print("Enter your custom prompt for Services-Hero2.jpg:")
    print("(Replace 'xxxx' with your desired description)")
    custom_prompt = input("Prompt: ").strip()
    
    if not custom_prompt or custom_prompt.lower() == 'xxxx':
        print("Using default EDA prompt...")
        custom_prompt = "Advanced EDA engineering laboratory with modern equipment, electromagnetic field simulations, professional workspace, high-tech atmosphere"
    
    # Generate image
    success = generate_services_hero2(custom_prompt)
    
    if success:
        print(f"\n🎉 Generation Complete!")
        print("Next steps:")
        print("1. Check Website-Images2/Services/Services-Hero2.jpg")
        print("2. Use python3 generate_services_hero2.py for integration options")
        print("3. Or manually update your CSS to use the new image")
    else:
        print(f"\n❌ Generation failed!")

if __name__ == "__main__":
    main()
