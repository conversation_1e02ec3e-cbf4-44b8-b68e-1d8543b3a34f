#!/bin/bash

# 🗂️ EDA Easy Project Reorganization Script
# Creates a clean, well-organized project structure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🗂️ Reorganizing EDA Easy Project Structure...${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Create new organized directory structure
echo -e "${BLUE}📁 Creating organized directory structure...${NC}"

# Create main directories
mkdir -p organized-project/{frontend,backend,deployment,docs,assets,tests}

# Create frontend subdirectories
mkdir -p organized-project/frontend/{pages,styles,scripts,images}

# Create backend subdirectories  
mkdir -p organized-project/backend/{lambda,reference,utils}

# Create deployment subdirectories
mkdir -p organized-project/deployment/{aws,scripts,configs}

# Create docs subdirectories
mkdir -p organized-project/docs/{design,api,user-guide}

# Create assets subdirectories
mkdir -p organized-project/assets/{images,videos,sample-files}

print_status "Directory structure created"

# Move frontend files
echo -e "${BLUE}📄 Organizing frontend files...${NC}"

# HTML pages
cp *.html organized-project/frontend/pages/
print_status "HTML pages moved to frontend/pages/"

# CSS files
cp -r css/* organized-project/frontend/styles/
print_status "CSS files moved to frontend/styles/"

# JavaScript files
cp -r js/* organized-project/frontend/scripts/
print_status "JavaScript files moved to frontend/scripts/"

# Images
cp -r Website-Images2/* organized-project/frontend/images/
print_status "Images moved to frontend/images/"

# Move backend files
echo -e "${BLUE}🐍 Organizing backend files...${NC}"

# Lambda function
cp lambda_function.py organized-project/backend/lambda/ 2>/dev/null || echo "lambda_function.py not found"

# Reference implementations
cp plot_s21_simple.py organized-project/backend/reference/
cp web_s21_backend.py organized-project/backend/reference/
cp ntwk_1.py organized-project/backend/reference/ 2>/dev/null || echo "ntwk_1.py not found"

print_status "Backend files moved to backend/"

# Move deployment files
echo -e "${BLUE}🚀 Organizing deployment files...${NC}"

# AWS deployment scripts
cp deploy-aws.sh organized-project/deployment/aws/
cp sync-to-aws.sh organized-project/deployment/aws/
cp cleanup-aws.sh organized-project/deployment/aws/
cp fix-*.sh organized-project/deployment/scripts/ 2>/dev/null || true
cp test-*.sh organized-project/deployment/scripts/ 2>/dev/null || true

print_status "Deployment files moved to deployment/"

# Move documentation
echo -e "${BLUE}📚 Organizing documentation...${NC}"

cp DESIGN_DOCUMENTATION.md organized-project/docs/design/
cp ISSUE_ANALYSIS.md organized-project/docs/design/
cp HOSTING_GUIDE_FULLSTACK.md organized-project/docs/design/
cp AWS_AUTOMATION_GUIDE.md organized-project/docs/design/

print_status "Documentation moved to docs/"

# Move assets
echo -e "${BLUE}🎨 Organizing assets...${NC}"

# Sample files
cp -r snpfiles/* organized-project/assets/sample-files/ 2>/dev/null || echo "No sample files found"

# Videos
cp -r videos/* organized-project/assets/videos/ 2>/dev/null || echo "No videos found"

print_status "Assets organized"

# Create updated HTML files with correct paths
echo -e "${BLUE}🔧 Updating file paths in HTML files...${NC}"

# Update HTML files to use new structure
for html_file in organized-project/frontend/pages/*.html; do
    if [ -f "$html_file" ]; then
        # Update CSS paths
        sed -i 's|css/|../styles/|g' "$html_file"
        
        # Update JS paths
        sed -i 's|js/|../scripts/|g' "$html_file"
        
        # Update image paths
        sed -i 's|Website-Images2/|../images/|g' "$html_file"
        
        # Update favicon path
        sed -i 's|Website-Images2/favicon.png|../images/favicon.png|g' "$html_file"
        
        print_status "Updated paths in $(basename "$html_file")"
    fi
done

# Create project README
cat > organized-project/README.md << 'EOF'
# 🏗️ EDA Easy - Organized Project Structure

## 📁 Directory Structure

```
organized-project/
├── 📄 frontend/           # Frontend web application
│   ├── pages/            # HTML pages
│   ├── styles/           # CSS stylesheets
│   ├── scripts/          # JavaScript files
│   └── images/           # Images and favicon
│
├── 🐍 backend/            # Backend services
│   ├── lambda/           # AWS Lambda functions
│   ├── reference/        # Reference implementations
│   └── utils/            # Utility functions
│
├── 🚀 deployment/         # Deployment automation
│   ├── aws/              # AWS deployment scripts
│   ├── scripts/          # Utility scripts
│   └── configs/          # Configuration files
│
├── 📚 docs/               # Documentation
│   ├── design/           # Design documentation
│   ├── api/              # API documentation
│   └── user-guide/       # User guides
│
├── 🎨 assets/             # Static assets
│   ├── images/           # Image files
│   ├── videos/           # Video files
│   └── sample-files/     # Sample S-parameter files
│
└── 🧪 tests/              # Test files
    ├── unit/             # Unit tests
    ├── integration/      # Integration tests
    └── e2e/              # End-to-end tests
```

## 🚀 Quick Start

### Development
```bash
# Serve frontend locally
cd frontend/pages
python3 -m http.server 8000

# Deploy to AWS
cd deployment/aws
./deploy-aws.sh
```

### Testing
```bash
# Run tests
cd deployment/scripts
./test-deployment.sh
```

## 📚 Documentation

- **Design Documentation**: `docs/design/DESIGN_DOCUMENTATION.md`
- **API Documentation**: `docs/api/`
- **User Guide**: `docs/user-guide/`

## 🔧 Maintenance

- **Sync to AWS**: `deployment/aws/sync-to-aws.sh`
- **Cleanup AWS**: `deployment/aws/cleanup-aws.sh`
- **Fix Issues**: `deployment/scripts/fix-*.sh`
EOF

print_status "Project README created"

# Create deployment script for organized structure
cat > organized-project/deploy-organized.sh << 'EOF'
#!/bin/bash

# 🚀 Deploy Organized EDA Easy Project

set -e

echo "🚀 Deploying organized EDA Easy project..."

# Copy files to deployment location
cp -r frontend/pages/* ./
cp -r frontend/styles ./css
cp -r frontend/scripts ./js
cp -r frontend/images ./Website-Images2

# Run AWS deployment
cd deployment/aws
./deploy-aws.sh

echo "✅ Organized project deployed successfully!"
EOF

chmod +x organized-project/deploy-organized.sh
print_status "Deployment script created"

# Create sync script for organized structure
cat > organized-project/sync-organized.sh << 'EOF'
#!/bin/bash

# 🔄 Sync Organized Project to AWS

set -e

echo "🔄 Syncing organized project to AWS..."

# Create temporary deployment directory
mkdir -p temp-deploy
cd temp-deploy

# Copy frontend files
cp -r ../frontend/pages/* ./
cp -r ../frontend/styles ./css
cp -r ../frontend/scripts ./js
cp -r ../frontend/images ./Website-Images2

# Sync to AWS
aws s3 sync . s3://$(aws s3 ls | grep "eda-easy-website" | awk '{print $3}' | head -1) \
    --exclude "*.sh" \
    --exclude "*.py" \
    --exclude "*.md" \
    --delete

# Cleanup
cd ..
rm -rf temp-deploy

echo "✅ Organized project synced to AWS!"
EOF

chmod +x organized-project/sync-organized.sh
print_status "Sync script created"

echo ""
echo -e "${GREEN}🎉 Project reorganization completed!${NC}"
echo ""
echo -e "${BLUE}📋 New Structure Summary:${NC}"
echo "✅ Frontend files organized in frontend/"
echo "✅ Backend files organized in backend/"
echo "✅ Deployment scripts organized in deployment/"
echo "✅ Documentation organized in docs/"
echo "✅ Assets organized in assets/"
echo "✅ Updated file paths in HTML files"
echo "✅ Created deployment scripts for organized structure"
echo ""
echo -e "${YELLOW}🎯 Next Steps:${NC}"
echo "1. Review the organized structure: cd organized-project"
echo "2. Test locally: cd organized-project/frontend/pages && python3 -m http.server 8000"
echo "3. Deploy organized version: cd organized-project && ./deploy-organized.sh"
echo "4. Use organized sync: ./sync-organized.sh"
echo ""
echo -e "${BLUE}📁 Organized project location: organized-project/${NC}"
