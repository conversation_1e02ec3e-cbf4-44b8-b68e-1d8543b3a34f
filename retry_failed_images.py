#!/usr/bin/env python3
"""
Retry failed image generation with simplified prompts
"""

import os
from dalle_image_generator import EDAImageGenerator

def main():
    print("🔄 Retry Failed Images")
    print("=" * 30)
    
    # Check API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OpenAI API key not found!")
        print("Please set: export OPENAI_API_KEY='your-key-here'")
        return
    
    # Initialize generator
    generator = EDAImageGenerator(api_key)
    
    # Check which images are missing
    missing_images = []
    for image_path in generator.image_definitions.keys():
        full_path = generator.output_dir / image_path
        if not full_path.exists():
            missing_images.append(image_path)
    
    if not missing_images:
        print("✅ All images already exist!")
        return
    
    print(f"Found {len(missing_images)} missing images:")
    for i, path in enumerate(missing_images, 1):
        print(f"  {i}. {path}")
    
    print(f"\n💰 Cost estimate: ~${len(missing_images) * 0.04:.2f}")
    proceed = input("Retry generation? (y/n): ").lower().strip()
    
    if proceed != 'y':
        print("👋 Cancelled!")
        return
    
    # Generate missing images
    print("\n🎨 Generating missing images...")
    results = {}
    
    for i, image_path in enumerate(missing_images, 1):
        print(f"\n[{i}/{len(missing_images)}] Processing: {image_path}")
        config = generator.image_definitions[image_path]
        success = generator.generate_single_image(image_path, config)
        results[image_path] = success
        
        # Add delay between requests
        if i < len(missing_images) and success:
            print("   ⏳ Waiting 3s before next request...")
            import time
            time.sleep(3)
    
    # Print summary
    successful = sum(1 for success in results.values() if success)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("RETRY SUMMARY")
    print("=" * 50)
    print(f"✅ Successful: {successful}/{total}")
    print(f"❌ Failed: {total - successful}/{total}")
    
    if successful < total:
        print("\nStill failed:")
        for path, success in results.items():
            if not success:
                print(f"  ❌ {path}")
        print("\n💡 Try running this script again or check your API key/credits")

if __name__ == "__main__":
    main()
