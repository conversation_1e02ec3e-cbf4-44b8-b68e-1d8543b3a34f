import tkinter as tk
from tkinter import ttk, filedialog, messagebox
# import skrf
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import ntwk_1


class TouchstoneGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Touchstone File Analyzer")
        self.root.geometry("1600x1400")  # Set initial window size

        # Initialize variables
        self.ntwk = None
        self.connectivity = tk.StringVar(value='')
        self.current_plot = None  # To track the current plot displayed

        # Create GUI elements
        self.conn1, self.conn2 = 'Port 1 ↔ Port 2', 'Port 1 ↔ Port N+1'
        self.connectivity_menu = None
        self.fig, self.axs = None, None

        self.create_widgets()

    def create_widgets(self):
        # Create a style for buttons with rounded corners and 3D appearance
        style = ttk.Style()
        style.configure('Rounded.TButton', corneradius=10, relief=tk.RAISED, font=('Arial', 12))

        # Main frame with light grey background
        main_frame = tk.Frame(self.root, bg='#f2f2f2')  # Very light grey
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Frame for steps and labels (top 30%)
        steps_frame = tk.Frame(main_frame, height=180, bg='#f2f2f2', bd=1,
                               relief=tk.SUNKEN)  # Very light grey with border
        steps_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=10)

        # Import Touchstone File Step
        import_label = tk.Label(steps_frame, text="Load Data", font=('Arial', 14, 'bold'), bg='#f2f2f2')
        import_label.grid(row=0, column=0, padx=10, pady=5, sticky='w')

        import_button = ttk.Button(steps_frame, text="Browse...", command=self.import_file, style='Rounded.TButton')
        import_button.grid(row=1, column=0, padx=10, pady=5, sticky='w')

        # Separation line between Import and Select Connectivity
        ttk.Separator(steps_frame, orient=tk.VERTICAL).grid(row=0, column=1, rowspan=2, sticky='ns', padx=10)

        # Select Connectivity Step
        self.connectivity_label = tk.Label(steps_frame, text="Thru Path", font=('Arial', 14, 'bold'), bg='#f2f2f2')
        self.connectivity_label.grid(row=0, column=2, padx=10, pady=5, sticky='w')

        port_label = tk.Label(steps_frame, text="Port 1 ↔ ", font=('Arial', 14, 'bold'), bg='#f2f2f2')
        port_label.grid(row=1, column=2, padx=10, pady=5, sticky='e')

        self.connectivity_menu = ttk.Combobox(steps_frame, textvariable=self.connectivity,
                                              values=[],
                                              font=('Arial', 12), width=14, state='readonly')
        self.connectivity_menu.grid(row=1, column=2, padx=10, pady=5, sticky='w')

        # Bind selection change event to update buttons
        self.connectivity_menu.bind("<<ComboboxSelected>>", self.update_buttons)

        # Separation line between Select Connectivity and Plot Options
        ttk.Separator(steps_frame, orient=tk.VERTICAL).grid(row=0, column=3, rowspan=2, sticky='ns', padx=10)

        # Plot Options Step
        plot_label = tk.Label(steps_frame, text="Plot Options", font=('Arial', 14, 'bold'), bg='#f2f2f2')
        plot_label.grid(row=0, column=6, padx=10, pady=5, sticky='w')

        plot_il_button = ttk.Button(steps_frame, text="IL", command=self.plot_insertion_loss,
                                    style='Rounded.TButton')
        plot_il_button.grid(row=1, column=4, padx=10, pady=5, sticky='w')

        plot_rl_button = ttk.Button(steps_frame, text="RL", command=self.plot_return_loss, style='Rounded.TButton')
        plot_rl_button.grid(row=1, column=5, padx=10, pady=5, sticky='w')

        self.plot_next_button = ttk.Button(steps_frame, text="NEXT", command=self.plot_next,
                                           style='Rounded.TButton')
        self.plot_next_button.grid(row=1, column=6, padx=10, pady=5, sticky='w')

        self.plot_fext_button = ttk.Button(steps_frame, text="FEXT", command=self.plot_fext,
                                           style='Rounded.TButton')
        self.plot_fext_button.grid(row=1, column=7, padx=10, pady=5, sticky='w')

        self.plot_grids_button = ttk.Button(steps_frame, text="Grid Plot", command=self.plot_grid, style='Rounded.TButton')
        self.plot_grids_button.grid(row=1, column=8, padx=10, pady=5, sticky='w')

        self.plot_table_button = ttk.Button(steps_frame, text="Summary Table", command=self.plot_table, style='Rounded.TButton')
        self.plot_table_button.grid(row=1, column=9, padx=10, pady=5, sticky='w')

        save_button = ttk.Button(steps_frame, text="Save Plot", command=self.save_plot, style='Rounded.TButton')
        save_button.grid(row=1, column=10, padx=10, pady=5, sticky='w')


        # Frame for plot (bottom 70%)
        plot_frame = tk.Frame(main_frame, bg='white')
        plot_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a figure for plotting
        self.fig, self.axs = plt.subplots(figsize=(8, 5))
        self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
        self.axs.set_ylabel('Magnitude (dB)', fontsize=14, weight='bold')
        self.axs.set_title('', fontsize=16, weight='bold')  # Initialize title with larger bold font
        self.axs.grid(True)
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        # Status Bar
        self.status_label = tk.Label(self.root, text="Ready", bd=1, relief=tk.SUNKEN, anchor=tk.W, font=('Arial', 12))
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

    def import_file(self):
        filename = filedialog.askopenfilename(filetypes=[("Touchstone files", "*.s*p")])
        self.status_label.config(text='importing...')
        if filename:
            try:
                self.ntwk = ntwk_1.Network(filename)
                self.status_label.config(text=f'Success, {filename} imported successfully.')
                # messagebox.showinfo("Success", f"File '{filename}' imported successfully.")

                # Change label and dropdown values if an s2p file is imported
                if filename.endswith('.s2p'):
                    self.connectivity_label.config(text="Device Type")
                    self.connectivity_menu['values'] = ['Series L', 'Diff. L', 'Capacitor', 'Transmission Line']
                    self.connectivity_menu.set('Series L')  # Default selection
                else:
                    n_ports = self.ntwk.number_of_ports
                    self.connectivity_label.config(text="Thru Path")
                    conn_values = ['Port 2', f'Port {1 + n_ports // 2}']
                    conn_values = ['Port 1 ↔ ' + string for string in conn_values]
                    self.conn2, self.conn1 = conn_values
                    self.connectivity_menu['values'] = conn_values
                    self.connectivity_menu.set('Port 1 ↔ Port 2')
                self.update_buttons()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to import file: {str(e)}")

    def update_buttons(self, event=None):
        # self.connectivity_menu.set('Port 1 ↔ Port 2')
        self.fig.clear()
        self.canvas.draw()

        # if self.ntwk:
        #     n_ports = self.ntwk.number_of_ports
        #     if n_ports == 2:
        #         # conn_values = ['Port 2']
        #         # conn_values = ['Port 1 ↔ ' + string for string in conn_values]
        #         # self.conn2, self.conn1 = conn_values[0], conn_values[0]
        #
        #         self.connectivity_label.config(text="Device Type")
        #         self.connectivity_menu.set('')
        #         conn_values = ['Series L', 'Diff. L', 'Capacitor', 'Transmission Line']
        #         self.connectivity_menu['values'] = conn_values
        #         device_type = self.connectivity.get()
        #         # while True:
        #         #     device_type = self.connectivity.get()
        #         #     if device_type in conn_values:
        #         #         break
        #
        #     else:
        #         self.connectivity_label.config(text="Thru Path")
        #         conn_values = ['Port 2', f'Port {1 + n_ports // 2}']
        #         conn_values = ['Port 1 ↔ ' + string for string in conn_values]
        #         self.conn2, self.conn1 = conn_values
        #         self.connectivity_menu['values'] = conn_values

        if self.ntwk and self.ntwk.number_of_ports == 2:
            device_type = self.connectivity.get()
            print(device_type)

            # Reset button states to default
            self.plot_next_button.config(state=tk.NORMAL)
            self.plot_fext_button.config(state=tk.NORMAL)
            self.plot_grids_button.config(state=tk.NORMAL)
            self.plot_table_button.config(state=tk.NORMAL)

            if device_type == 'Series L':
                self.plot_next_button.config(command=self.plot_ind, text='Seirs L')
                self.plot_fext_button.config(command=self.plot_q, text='Q Factor')

                self.plot_grids_button.grid(row=1, column=8, padx=10, pady=5, sticky='w')
                self.plot_table_button.grid(row=1, column=9, padx=10, pady=5, sticky='w')
                self.plot_grids_button.config(command=self.plot_grid, text='Grid Plot')
                self.plot_table_button.config(command=self.plot_table, text='Summary Table')



            elif device_type == 'Diff. L':
                self.plot_next_button.config(command=self.plot_diff_ind, text='Diff. L')
                self.plot_fext_button.config(command=self.plot_q, text='Q Factor')

                self.plot_grids_button.grid(row=1, column=8, padx=10, pady=5, sticky='w')
                self.plot_table_button.grid(row=1, column=9, padx=10, pady=5, sticky='w')
                self.plot_grids_button.config(command=self.plot_grid, text='Grid Plot')
                self.plot_table_button.config(command=self.plot_table, text='Summary Table')

            elif device_type == 'Capacitor':
                self.plot_next_button.config(command=self.plot_cap, text='Cap.')
                self.plot_fext_button.config(state=tk.DISABLED)
                self.plot_grids_button.config(state=tk.DISABLED)
                self.plot_table_button.config(state=tk.DISABLED)

            elif device_type == 'Transmission Line':
                self.plot_next_button.config(command=self.plot_res, text='Impedance')
                self.plot_fext_button.config(state=tk.DISABLED)
                self.plot_grids_button.config(state=tk.DISABLED)
                self.plot_table_button.config(state=tk.DISABLED)

            else:
                print(f'warning, unsupported device type')
                self.plot_next_button.config(state=tk.DISABLED)
                self.plot_fext_button.config(state=tk.DISABLED)
                self.plot_grids_button.config(state=tk.DISABLED)
                self.plot_table_button.config(state=tk.DISABLED)

            # pre_calulation of all performance matrix for a 2-port s-para
            y_params = self.ntwk.y
            # Create subplot for insertion loss
            # self.freq = self.ntwk.freq/ 1e9  # Convert to GHz
            self.freq = self.ntwk.freq / 1e9
            self.reactance = np.imag(1 / y_params[:, 0, 0])  # self.ntwk.inductance_s.to('nH')
            self.ind = np.imag(1 / y_params[:, 0, 0]) / (2 * np.pi * self.freq)  # self.ntwk.inductance_s.to('nH')
            self.resistance = np.real(1 / y_params[:, 0, 0])
            self.q_value = self.reactance / self.resistance

            # Identify key points
            self.fpk = self.freq[np.argmax(self.q_value)]  # Frequency at max Q
            self.fsr = self.freq[np.argmin(np.abs(self.ind))]  # Frequency at smallest |L|
            self.Ldc = self.ind[0]  # Inductance at DC
            self.Lpk = self.ind[np.argmax(self.q_value)]  # Inductance at Fpk
            self.Qpk = np.max(self.q_value)  # Max Q
            self.Rdc = self.resistance[0]  # resistance at DC

        elif self.ntwk and self.ntwk.number_of_ports != 2:
            self.plot_next_button.config(state=tk.NORMAL)
            self.plot_fext_button.config(state=tk.NORMAL)

            self.plot_next_button.config(command=self.plot_next, text='NEXT')
            self.plot_fext_button.config(command=self.plot_fext, text='FEXT')
            self.plot_grids_button.grid_forget()
            self.plot_table_button.grid_forget()

    def determine_insertion_loss(self):
        num_ports = self.ntwk.number_of_ports
        if num_ports == 2:
            return [(0, 1)]

        if self.connectivity.get() == self.conn1:
            return [(i, num_ports // 2 + i) for i in range(num_ports // 2)]
        elif self.connectivity.get() == self.conn2:
            return [(i, i + 1) for i in range(0, num_ports, 2)]
        else:
            raise ValueError(f"Unsupported connectivity type: {self.connectivity.get()}")

    def determine_NEXT(self):
        num_ports = self.ntwk.number_of_ports
        # params = []

        if self.connectivity.get() == self.conn1:
            return [(i, j) for i in range(num_ports // 2) for j in range(num_ports // 2) if i != j]
        elif self.connectivity.get() == self.conn2:
            return [(i, j) for i in range(0, num_ports, 2) for j in range(0, num_ports, 2) if i != j]
        else:
            raise ValueError(f"Unsupported connectivity type: {self.connectivity.get()}")

    def determine_FEXT(self):
        num_ports = self.ntwk.number_of_ports

        if self.connectivity.get() == self.conn1:
            return [(i, j) for i in range(0, num_ports // 2) for j in range(num_ports // 2, num_ports) if
                    i + num_ports // 2 != j]
        elif self.connectivity.get() == self.conn2:
            return [(i, j) for i in range(0, num_ports, 2) for j in range(1, num_ports, 2) if i + 1 != j]
        else:
            raise ValueError(f"Unsupported connectivity type: {self.connectivity.get()}")

    def plot_insertion_loss(self):
        # if self.current_plot == 'Insertion Loss':
        #     messagebox.showinfo("Info", "Insertion Loss plot is already displayed.")
        #     return

        if self.ntwk:
            params = self.determine_insertion_loss()

            # Clear previous plot
            self.fig.clear()
            # self.axs.clear()

            # Create subplot for insertion loss
            self.axs = self.fig.add_subplot(111)

            for m, n in params:
                freq = self.ntwk.freq/ 1e9  # Convert to GHz
                s_db = self.ntwk.s_db[:, n, m]
                self.axs.plot(freq, s_db, label=f'S{n + 1}{m + 1}')

            self.axs.set_title('Insertion Loss', fontsize=16, weight='bold')
            self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
            self.axs.set_ylabel('Magnitude (dB)', fontsize=14, weight='bold')
            self.axs.legend()
            self.axs.grid(True)  # Ensure gridlines are enabled
            self.canvas.draw()

            # Update current plot
            self.current_plot = 'Insertion Loss'

        else:
            messagebox.showerror("Error", "No network data imported.")

    def plot_return_loss(self):
        # if self.current_plot == 'Return Loss':
        #     messagebox.showinfo("Info", "Return Loss plot is already displayed.")
        #     return

        if self.ntwk:
            # Clear previous plot
            self.fig.clear()
            # self.axs.clear()

            # Create subplot for insertion loss
            self.axs = self.fig.add_subplot(111)

            # Create subplot for return loss
            for i in range(self.ntwk.number_of_ports // 2):
                freq = self.ntwk.freq/ 1e9  # Convert to GHz
                s_db = self.ntwk.s_db[:, i, i]
                self.axs.plot(freq, s_db, label=f'S{i + 1}{i + 1}')

            self.axs.set_title('Return Loss', fontsize=16, weight='bold')
            self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
            self.axs.set_ylabel('Magnitude (dB)', fontsize=14, weight='bold')
            self.axs.legend()
            self.axs.grid(True)  # Ensure gridlines are enabled
            self.canvas.draw()

            # Update current plot
            self.current_plot = 'Return Loss'

        else:
            messagebox.showerror("Error", "No network data imported.")

    def plot_next(self):
        # Placeholder function for plotting NEXT (if implemented similarly to IL and RL)
        # messagebox.showinfo("Not Implemented", "NEXT plotting functionality not implemented yet.")
        #
        # if self.current_plot == 'Insertion Loss':
        #     messagebox.showinfo("Info", "Insertion Loss plot is already displayed.")
        #     return

        if self.ntwk:
            params = self.determine_NEXT()

            # Clear previous plot
            self.fig.clear()
            # self.axs.clear()

            # Create subplot for insertion loss
            self.axs = self.fig.add_subplot(111)

            # Create subplot for insertion loss
            for m, n in params:
                freq = self.ntwk.freq/ 1e9  # Convert to GHz
                s_db = self.ntwk.s_db[:, m, n]
                self.axs.plot(freq, s_db, label=f'S{m + 1}{n + 1}')

            self.axs.set_title('NEXT', fontsize=16, weight='bold')
            self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
            self.axs.set_ylabel('Magnitude (dB)', fontsize=14, weight='bold')
            self.axs.legend()
            self.axs.grid(True)  # Ensure gridlines are enabled
            self.canvas.draw()

            # Update current plot
            self.current_plot = 'Insertion Loss'

        else:
            messagebox.showerror("Error", "No network data imported.")

    def plot_fext(self):
        # Placeholder function for plotting FEXT (if implemented similarly to IL and RL)
        # messagebox.showinfo("Not Implemented", "FEXT plotting functionality not implemented yet.")

        if self.ntwk:
            params = self.determine_FEXT()

            # Clear previous plot
            self.fig.clear()
            # self.axs.clear()

            # Create subplot for insertion loss
            self.axs = self.fig.add_subplot(111)

            # Create subplot for insertion loss
            for m, n in params:
                freq = self.ntwk.freq/ 1e9  # Convert to GHz
                s_db = self.ntwk.s_db[:, m, n]
                self.axs.plot(freq, s_db, label=f'S{m + 1}{n + 1}')

            self.axs.set_title('FEXT', fontsize=16, weight='bold')
            self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
            self.axs.set_ylabel('Magnitude (dB)', fontsize=14, weight='bold')
            self.axs.legend()
            self.axs.grid(True)  # Ensure gridlines are enabled
            self.canvas.draw()

            # Update current plot
            self.current_plot = 'Insertion Loss'

        else:
            messagebox.showerror("Error", "No network data imported.")

    def plot_res(self):
        # Placeholder function for plotting NEXT (if implemented similarly to IL and RL)
        # messagebox.showinfo("Not Implemented", "Capacitance plotting functionality not implemented yet.")

        if self.ntwk:
            # Clear previous plot
            self.fig.clear()
            # self.axs.clear()

            # Create subplot for insertion loss
            self.axs = self.fig.add_subplot(111)

            z_params = self.ntwk.z
            # Create subplot for insertion loss
            freq = self.ntwk.freq/ 1e9  # Convert to GHz
            res = np.real(1 / z_params[:, 0, 0])  # self.ntwk.inductance_s.to('nH')
            self.axs.plot(freq, res, label='Series R')

            self.axs.set_title('Series R', fontsize=16, weight='bold')
            self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
            self.axs.set_ylabel('Series R (Ohm)', fontsize=14, weight='bold')
            self.axs.legend()
            self.axs.grid(True)  # Ensure gridlines are enabled
            self.canvas.draw()

            # Update current plot
            self.current_plot = 'Series R'

        else:
            messagebox.showerror("Error", "No network data imported.")

    def plot_cap(self):
        # Placeholder function for plotting NEXT (if implemented similarly to IL and RL)
        # messagebox.showinfo("Not Implemented", "Capacitance plotting functionality not implemented yet.")

        if self.ntwk:
            # Clear previous plot
            self.fig.clear()
            # self.axs.clear()

            # Create subplot for insertion loss
            self.axs = self.fig.add_subplot(111)

            z_params = self.ntwk.z
            # Create subplot for insertion loss
            freq = self.ntwk.freq/ 1e9  # Convert to GHz
            cap = np.imag(1 / z_params[:, 0, 0]) / (2 * np.pi * freq)  # self.ntwk.inductance_s.to('nH')
            self.axs.plot(freq, cap, label='Shunt C')

            self.axs.set_title('Shunt C', fontsize=16, weight='bold')
            self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
            self.axs.set_ylabel('Shunt C (pF)', fontsize=14, weight='bold')
            self.axs.legend()
            self.axs.grid(True)  # Ensure gridlines are enabled
            self.canvas.draw()

            # Update current plot
            self.current_plot = 'Shunt C'

        else:
            messagebox.showerror("Error", "No network data imported.")

    def plot_ind(self):
        # Placeholder function for plotting NEXT (if implemented similarly to IL and RL)
        # messagebox.showinfo("Not Implemented", "Inductance plotting functionality not implemented yet.")

        if self.ntwk:
            # Clear previous plot
            self.fig.clear()
            # self.axs.clear()

            # Create subplot for insertion loss
            self.axs = self.fig.add_subplot(111)

            y_params = self.ntwk.y
            # Create subplot for insertion loss
            freq = self.ntwk.freq/ 1e9  # Convert to GHz
            ind = np.imag(1 / y_params[:, 0, 0]) / (2 * np.pi * freq)  # self.ntwk.inductance_s.to('nH')
            self.axs.plot(freq, ind, label='Inductance')

            self.axs.axvline(x=self.fpk, color='grey', linestyle='--')
            self.axs.axvline(x=self.fsr, color='grey', linestyle='--')

            self.axs.text(freq[0], ind[0], f'Ldc: {ind[0]:.2f} nH',
                          verticalalignment='center',
                          horizontalalignment='left', fontsize=10)
            self.axs.text(self.fpk, self.Lpk, f'Fpk: {self.fpk:.2f} GHz\nLpk: {self.Lpk:.2f} nH',
                          verticalalignment='center',
                          horizontalalignment='left', fontsize=10)
            self.axs.text(self.fsr, self.axs.get_ylim()[0] + 0.75 * (self.axs.get_ylim()[1] - self.axs.get_ylim()[0]),
                         f'Fsr: {self.fsr:.2f} GHz',
                          verticalalignment='center',
                          horizontalalignment='left', fontsize=10)

            self.axs.set_title('Inductance', fontsize=16, weight='bold')
            self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
            self.axs.set_ylabel('Inductance (nH)', fontsize=14, weight='bold')
            self.axs.legend()
            self.axs.grid(True)  # Ensure gridlines are enabled
            self.canvas.draw()

            # Update current plot
            self.current_plot = 'Inductance'

        else:
            messagebox.showerror("Error", "No network data imported.")

    def plot_diff_ind(self):
        # Placeholder function for plotting NEXT (if implemented similarly to IL and RL)
        # messagebox.showinfo("Not Implemented", "Inductance plotting functionality not implemented yet.")

        if self.ntwk:
            # Clear previous plot
            self.fig.clear()
            # self.axs.clear()

            # Create subplot for insertion loss
            self.axs = self.fig.add_subplot(111)

            y_params = self.ntwk.y
            s_params = self.ntwk.s
            # Create subplot for insertion loss
            freq = self.ntwk.freq/ 1e9  # Convert to GHz
            z = s_params[:, 0, 0] - s_params[:, 0, 1] + s_params[:, 1, 1] - s_params[:, 1, 0]
            ind = np.imag(z) / (2 * np.pi * freq)  # self.ntwk.inductance_s.to('nH')
            self.axs.plot(freq, ind, label='Inductance')

            self.axs.axvline(x=self.fpk, color='grey', linestyle='--')
            self.axs.axvline(x=self.fsr, color='grey', linestyle='--')

            self.axs.text(freq[0], ind[0], f'Ldc: {ind[0]:.2f} nH',
                          verticalalignment='center',
                          horizontalalignment='left', fontsize=10)
            self.axs.text(self.fpk, self.Lpk, f'Fpk: {self.fpk:.2f} GHz\nLpk: {self.Lpk:.2f} nH',
                          verticalalignment='center',
                          horizontalalignment='left', fontsize=10)
            self.axs.text(self.fsr, self.axs.get_ylim()[0] + 0.75 * (self.axs.get_ylim()[1] - self.axs.get_ylim()[0]),
                         f'Fsr: {self.fsr:.2f} GHz',
                          verticalalignment='center',
                          horizontalalignment='left', fontsize=10)

            self.axs.set_title('Inductance', fontsize=16, weight='bold')
            self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
            self.axs.set_ylabel('Inductance (nH)', fontsize=14, weight='bold')
            self.axs.legend()
            self.axs.grid(True)  # Ensure gridlines are enabled
            self.canvas.draw()

            # Update current plot
            self.current_plot = 'Diff. Inductance'

        else:
            messagebox.showerror("Error", "No network data imported.")

    def plot_q(self):
        # Placeholder function for plotting NEXT (if implemented similarly to IL and RL)
        # messagebox.showinfo("Not Implemented", "Inductance plotting functionality not implemented yet.")

        if self.ntwk:
            # Clear previous plot
            self.fig.clear()
            # self.axs.clear()

            # Create subplot for insertion loss
            self.axs = self.fig.add_subplot(111)

            y_params = self.ntwk.y
            # Create subplot for insertion loss
            freq = self.ntwk.freq/ 1e9  # Convert to GHz
            reactance = np.imag(1 / y_params[:, 0, 0])  # self.ntwk.inductance_s.to('nH')
            res = np.real(1 / y_params[:, 0, 0])
            q_value = reactance / res
            self.axs.plot(freq, q_value, label='Q Factor')

            self.axs.axvline(x=self.fpk, color='grey', linestyle='--')
            self.axs.text(self.fpk, self.Qpk, f'Fpk: {self.fpk:.2f} GHz\nQpk: {self.Qpk:.2f}',
                          verticalalignment='center',
                          horizontalalignment='left', fontsize=10)

            self.axs.set_title('Q Factor', fontsize=16, weight='bold')
            self.axs.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
            self.axs.set_ylabel('Q Factor', fontsize=14, weight='bold')
            self.axs.legend()
            self.axs.grid(True)  # Ensure gridlines are enabled
            self.canvas.draw()

            # Update current plot
            self.current_plot = 'Q Factor'

        else:
            messagebox.showerror("Error", "No network data imported.")

    def plot_grid(self):
        if not self.ntwk:
            messagebox.showerror("Error", "No network data imported.")
            return

        y_params = self.ntwk.y
        # Create subplot for insertion loss
        freq = self.ntwk.freq/ 1e9  # Convert to GHz
        reactance = np.imag(1 / y_params[:, 0, 0])  # self.ntwk.inductance_s.to('nH')
        ind = np.imag(1 / y_params[:, 0, 0]) / (2 * np.pi * freq)  # self.ntwk.inductance_s.to('nH')
        resistance = np.real(1 / y_params[:, 0, 0])
        q_value = reactance / resistance

        # Identify key points
        fpk = freq[np.argmax(q_value)]  # Frequency at max Q
        fsr = freq[np.argmin(np.abs(ind))]  # Frequency at smallest |L|
        Ldc = ind[0]  # Inductance at DC
        Lpk = ind[np.argmax(q_value)]  # Inductance at Fpk
        Qpk = np.max(q_value)  # Max Q
        Rdc = resistance[0]  # resistance at DC

        # Clear previous plot
        self.fig.clear()
        # self.axs.clear()
        self.axs = self.fig.subplots(2, 2)
        # plt.subplots_adjust(hspace=0.4, wspace=0.3)  # Adjust the spacing between subplots

        self.plot_s_parameters(self.axs, freq, Rdc)
        self.plot_inductance(self.axs[0, 1], freq, ind, fpk, fsr, Lpk)
        self.plot_quality_factor(self.axs[1, 1], freq, q_value, fpk, Qpk)

        # Update current plot
        self.current_plot = 'Grid Plot'
        self.fig.tight_layout()
        self.canvas.draw()

    def plot_table(self):
        if not self.ntwk:
            messagebox.showerror("Error", "No network data imported.")
            return

        # Clear the plot and adjust for table display
        self.fig.clear()
        ax = self.fig.add_subplot(111)
        ax.axis('off')  # Turn off axis

        # Define the table data
        table_data = [
            ["Ldc (nH)", f"{self.Ldc:.2f}"],
            ["fpk (GHz)", f"{self.fpk:.2f}"],
            ["Qpk", f"{self.Qpk:.2f}"],
            ["fsr (GHz)", f"{self.fsr:.2f}"],
            ["Rdc (Ohm)", f"{self.Rdc:.2f}"]
        ]

        # Display the table
        table = ax.table(cellText=table_data, colLabels=["Parameter", "Value"], loc='center', cellLoc='left')
        table.auto_set_font_size(False)
        table.set_fontsize(10)  # Set font size
        table.scale(1.2, 1.2)  # Adjust scaling to make the table bigger

        self.canvas.draw()  # Redraw the canvas to show the table

        # # Add table below plots
        # table_data = {
        #     "Parameter": ["Ldc (nH)", "fpk (GHz)", "Lpk (nH)", "Qpk", "fsr (GHz)", "Rdc (Ohm)"],
        #     "Value": [f"{self.Ldc:.2f}", f"{self.fpk:.2f}", f"{self.Lpk:.2f}", f"{self.Qpk:.2f}", f"{self.fsr:.2f}", f"{self.Rdc:.2f}"]
        # }
        #
        # # Create a separate figure for the table
        # # table_fig, table_ax = plt.subplots(figsize=(8, 5))  # Smaller figure for the table
        # self.axs.axis('off')
        # self.axs.table(
        #     cellText=list(zip(*table_data.values())),
        #     colLabels=list(table_data.keys()),
        #     loc='center',
        #     cellLoc='left')
        # self.axs.auto_set_font_size(True)
        # # table.scale(1.5, 1.5)  # Increase table size
        # # plt.show()
        #
        # # Update current plot
        # self.current_plot = 'Summary Table'
        # self.canvas.draw()

    def plot_s_parameters(self, axs, freq, Rdc):
        # self.ntwk.plot_s_db(m=0, n=1, ax=axs[0, 0], color='blue')
        # axs[0, 0].get_legend().remove()
        # axs[0, 0].text(freq[0], -10, f'Rdc: {Rdc:.2f} Ohm', fontsize=10, ha='left')
        # axs[0, 0].set_title('Insertion Loss', fontsize=16, weight='bold')
        # axs[0, 0].set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
        # axs[0, 0].set_ylabel('IL (dB)', fontsize=14, weight='bold')
        # axs[0, 0].grid(True)
        #
        # self.ntwk.plot_s_db(m=0, n=0, ax=axs[1, 0], color='blue')
        # self.ntwk.plot_s_db(m=1, n=1, ax=axs[1, 0], color='blue')
        # axs[1, 0].get_legend().remove()
        # axs[1, 0].set_title('Return Loss', fontsize=16, weight='bold')
        # axs[1, 0].set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
        # axs[1, 0].set_ylabel('RL (dB)', fontsize=14, weight='bold')
        # axs[1, 0].grid(True)

        # For the first subplot
        self.ntwk.plot_s_db(m=0, n=1, ax=axs[0, 0], color='blue', show_legend=False)
        axs[0, 0].text(freq[0], -10, f'Rdc: {Rdc:.2f} Ohm', fontsize=10, ha='left')
        axs[0, 0].set_title('Insertion Loss', fontsize=16)
        axs[0, 0].set_xlabel('Frequency (GHz)', fontsize=14)
        axs[0, 0].set_ylabel('IL (dB)', fontsize=14)
        axs[0, 0].grid(True)

        # For the second subplot
        self.ntwk.plot_s_db(m=0, n=0, ax=axs[1, 0], color='blue', show_legend=False)
        # self.plot_s_db(m=1, n=1, ax=axs[1, 0], color='blue', show_legend=False)
        axs[1, 0].set_title('Return Loss', fontsize=16)
        axs[1, 0].set_xlabel('Frequency (GHz)', fontsize=14)
        axs[1, 0].set_ylabel('RL (dB)', fontsize=14)
        axs[1, 0].grid(True)

    def plot_inductance(self, ax, freq, ind, fpk, fsr, Lpk):
        ax.plot(freq, ind, label='Inductance (nH)')

        ax.plot(freq, ind, label='Inductance (nH)', color='blue')
        ax.axvline(x=fpk, color='grey', linestyle='--')
        ax.axvline(x=fsr, color='grey', linestyle='--')

        ax.text(freq[0], ind[0], f'Ldc: {ind[0]:.2f} nH',
                verticalalignment='center',
                horizontalalignment='left', fontsize=10)
        ax.text(fpk, Lpk, f'Fpk: {fpk:.2f} GHz\nLpk: {Lpk:.2f} nH',
                verticalalignment='center',
                horizontalalignment='left', fontsize=10)
        ax.text(fsr, ax.get_ylim()[0] + 0.75 * (ax.get_ylim()[1] - ax.get_ylim()[0]), f'Fsr: {fsr:.2f} GHz',
                verticalalignment='center',
                horizontalalignment='left', fontsize=10)

        ax.set_title('Inductance', fontsize=16, weight='bold')
        ax.set_xlabel('Frequency (GHz)', fontsize=14, weight='bold')
        ax.set_ylabel('Inductance (nH)', fontsize=14, weight='bold')
        ax.grid(True)

    def plot_quality_factor(self, ax, freq, q_value, fpk, Qpk):
        ax.plot(freq, q_value, label='Quality Factor', color='blue')
        ax.axvline(x=fpk, color='grey', linestyle='--')

        ax.text(fpk, Qpk, f'Fpk: {fpk:.2f} GHz\nQpk: {Qpk:.2f}', verticalalignment='center',
                horizontalalignment='left', fontsize=10)

        ax.set_title("Quality Factor", fontsize=16, weight='bold')
        ax.set_xlabel("Frequency (GHz)", fontsize=14, weight='bold')
        ax.set_ylabel("Q Factor", fontsize=14, weight='bold')
        ax.grid(True)

    def save_plot(self):
        if self.fig:
            file_path = filedialog.asksaveasfilename(defaultextension=".png", filetypes=[("PNG files", "*.png"), ("All Files", "*.*")])
            if file_path:
                self.fig.savefig(file_path)
                messagebox.showinfo("Save Plot", f"Plot saved as {file_path}")
        else:
            messagebox.showerror("Save Error", "No plot to save")

    def run(self):
        self.root.mainloop()


def main():
    root = tk.Tk()
    app = TouchstoneGUI(root)
    root.geometry("1200x900")  # Set initial window size
    # root.iconbitmap(r'G:\myPython\favicon_1.ico')  # Set default tkinter icon
    app.run()


if __name__ == "__main__":
    """
    # to build the standalone
    # https://www.icoconverter.com/
    # pyinstaller --onefile --windowed --icon=<xxx.ico> rf_process_gui.py
    # pyinstaller --onefile --windowed rf_process5.py
    """
    main()
