#!/usr/bin/env python3
"""
Simple step-by-step runner for EDA image generation
"""

import os
import sys
import subprocess

def check_dependencies():
    """Check if required packages are installed"""
    try:
        import requests
        import openai
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False

def main():
    print("🚀 EDA Image Generation - Step by Step Guide")
    print("=" * 60)
    
    print("\n📋 STEP 1: Check Dependencies")
    if not check_dependencies():
        print("Installing dependencies...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "requests", "openai"], check=True)
            print("✅ Dependencies installed!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies. Please run:")
            print("   pip install requests openai")
            return
    else:
        print("✅ All dependencies are installed!")
    
    print("\n📋 STEP 2: API Key Setup")
    print("You need an OpenAI API key to generate images.")
    print("1. Go to: https://platform.openai.com/api-keys")
    print("2. Sign in to your OpenAI account")
    print("3. Click 'Create new secret key'")
    print("4. Copy the key (starts with 'sk-proj-' or 'sk-')")
    
    input("\nPress Enter when you have your API key ready...")
    
    print("\n📋 STEP 3: Generate Images")
    print("The script will now:")
    print("✓ Ask for your API key")
    print("✓ Show cost estimate (~$0.80 for 20 images)")
    print("✓ Generate all 20 EDA images with correct names")
    print("✓ Save them in Website-Images2/ folder")
    print("✓ Show progress and final summary")
    
    proceed = input("\nReady to start? (y/n): ").lower().strip()
    if proceed != 'y':
        print("👋 Setup cancelled!")
        return
    
    print("\n🎨 Starting image generation...")
    print("=" * 40)
    
    # Import and run the main generator
    try:
        from dalle_image_generator import main as generate_images
        generate_images()
    except Exception as e:
        print(f"❌ Error running generator: {e}")
        print("\nTry running directly:")
        print("   python3 dalle_image_generator.py")

if __name__ == "__main__":
    main()
