# 🚀 Scripts Directory

This directory contains all automation scripts for the EDA Easy website project.

## 📋 **Script Categories**

### **🏗️ Deployment Scripts**
| Script | Purpose | Usage |
|--------|---------|-------|
| `deploy-aws.sh` | Full AWS deployment from scratch | `./scripts/deploy-aws.sh` |
| `sync-to-aws.sh` | Sync changes to existing AWS deployment | `./scripts/sync-to-aws.sh` |
| `cleanup-aws.sh` | Remove all AWS resources | `./scripts/cleanup-aws.sh` |
| `update-lambda.sh` | Update Lambda function only | `./scripts/update-lambda.sh` |

### **🧪 Testing Scripts**
| Script | Purpose | Usage |
|--------|---------|-------|
| `test-deployment.sh` | Test AWS deployment health | `./scripts/test-deployment.sh` |
| `test-website.sh` | Test local website functionality | `./scripts/test-website.sh` |

### **🔧 Setup Scripts**
| Script | Purpose | Usage |
|--------|---------|-------|
| `setup_snp_server.sh` | Setup S-parameter processing environment | `./scripts/setup_snp_server.sh` |
| `setup_dalle_generator.sh` | Setup DALL-E image generation | `./scripts/setup_dalle_generator.sh` |
| `start_web_backend.sh` | Start local backend server | `./scripts/start_web_backend.sh` |
| `start_demo.sh` | Start demo environment | `./scripts/start_demo.sh` |

### **🔨 Fix Scripts**
| Script | Purpose | Usage |
|--------|---------|-------|
| `fix-all-issues.sh` | Run all available fixes | `./scripts/fix-all-issues.sh` |
| `fix-deployment.sh` | Fix deployment issues | `./scripts/fix-deployment.sh` |
| `fix-lambda-final.sh` | Fix Lambda function issues | `./scripts/fix-lambda-final.sh` |
| `fix-mobile-menu.sh` | Fix mobile menu issues | `./scripts/fix-mobile-menu.sh` |
| `fix-s21-accuracy.sh` | Fix S21 plotting accuracy | `./scripts/fix-s21-accuracy.sh` |
| `fix-s21-exact.sh` | Fix S21 exact data processing | `./scripts/fix-s21-exact.sh` |
| `quick-fix-s21.sh` | Quick S21 fixes | `./scripts/quick-fix-s21.sh` |

### **🔄 Utility Scripts**
| Script | Purpose | Usage |
|--------|---------|-------|
| `clean-and-redeploy.sh` | Clean and redeploy everything | `./scripts/clean-and-redeploy.sh` |
| `reorganize-project.sh` | Reorganize project structure | `./scripts/reorganize-project.sh` |
| `use-exact-method.sh` | Switch to exact S21 method | `./scripts/use-exact-method.sh` |

---

## 🎯 **Common Workflows**

### **First Time Setup**
```bash
# 1. Setup environment
./scripts/setup_snp_server.sh

# 2. Deploy to AWS
./scripts/deploy-aws.sh

# 3. Test deployment
./scripts/test-deployment.sh
```

### **Development Workflow**
```bash
# 1. Start local development
./scripts/start_web_backend.sh

# 2. Make changes to code
# 3. Test locally
./scripts/test-website.sh

# 4. Deploy changes
./scripts/sync-to-aws.sh
```

### **Troubleshooting Workflow**
```bash
# 1. Try automated fixes
./scripts/fix-all-issues.sh

# 2. If still broken, clean slate
./scripts/clean-and-redeploy.sh

# 3. Test everything
./scripts/test-deployment.sh
```

---

## 🔧 **Script Usage Guidelines**

### **Before Running Scripts**
1. **Check prerequisites** - Ensure AWS CLI is configured
2. **Read script comments** - Each script has usage instructions at the top
3. **Test locally first** - Use test scripts before deploying
4. **Backup important data** - Some scripts are destructive

### **Script Permissions**
```bash
# Make all scripts executable
chmod +x scripts/*.sh

# Or individually
chmod +x scripts/deploy-aws.sh
```

### **Running Scripts**
```bash
# From project root
./scripts/script-name.sh

# Or from scripts directory
cd scripts
./script-name.sh
```

---

## 🚨 **Destructive Scripts (Use with Caution)**

| Script | What it destroys | Recovery |
|--------|------------------|----------|
| `cleanup-aws.sh` | All AWS resources | Run `deploy-aws.sh` |
| `clean-and-redeploy.sh` | AWS resources + redeploys | Automatic recovery |
| `reorganize-project.sh` | Project structure | Manual file restoration |

---

## 📝 **Adding New Scripts**

When creating new scripts:

1. **Use descriptive names** with category prefix:
   - `deploy-*` for deployment
   - `fix-*` for fixes
   - `setup-*` for setup
   - `test-*` for testing

2. **Add script header**:
```bash
#!/bin/bash
# Script Name: Description of what it does
# Usage: ./script-name.sh [options]
# Prerequisites: List any requirements

set -e  # Exit on error
```

3. **Update this README** with the new script information

4. **Test thoroughly** before committing

---

## 🔍 **Script Dependencies**

### **Required Tools**
- `bash` (all scripts)
- `aws` CLI (deployment scripts)
- `python3` (setup scripts)
- `curl` (test scripts)
- `zip` (deployment scripts)

### **Environment Variables**
Some scripts may require:
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `OPENAI_API_KEY` (for DALL-E scripts)

---

## 📞 **Getting Help**

- **Script fails**: Check the error message and prerequisites
- **AWS issues**: See `../docs/AWS_DEPLOYMENT_GUIDE.md`
- **Local issues**: See `../docs/LOCAL_DEVELOPMENT_GUIDE.md`
- **General help**: Check `../docs/` directory for specific guides
