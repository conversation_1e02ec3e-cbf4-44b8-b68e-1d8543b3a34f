#!/bin/bash

# 🔧 Automatic Fix Script for EDA Easy AWS Deployment
# This script automatically detects and fixes deployment issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Auto-detect configuration
LAMBDA_FUNCTION_NAME="eda-easy-backend"
API_ID=""
BUCKET_NAME=""
REGION=""

echo -e "${BLUE}🔧 Automatic EDA Easy Deployment Fix...${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Auto-detect resources
echo -e "${BLUE}🔍 Auto-detecting AWS resources...${NC}"

# Find S3 bucket
BUCKET_NAME=$(aws s3 ls | grep "eda-easy-website" | awk '{print $3}' | head -1)
if [ -n "$BUCKET_NAME" ]; then
    print_status "Found S3 bucket: $BUCKET_NAME"
else
    print_error "No EDA Easy S3 bucket found"
    exit 1
fi

# Find API Gateway in both regions
for region in us-east-1 us-west-2; do
    api_check=$(aws apigateway get-rest-apis --region $region --query "items[?contains(name, 'eda-easy')].id" --output text 2>/dev/null || echo "")
    if [ -n "$api_check" ]; then
        API_ID="$api_check"
        REGION="$region"
        print_status "Found API Gateway: $API_ID in region: $REGION"
        break
    fi
done

if [ -z "$API_ID" ]; then
    print_error "No EDA Easy API Gateway found"
    exit 1
fi

# Find Lambda function in both regions
for region in us-east-1 us-west-2; do
    lambda_check=$(aws lambda list-functions --region $region --query "Functions[?contains(FunctionName, 'eda-easy')].FunctionName" --output text 2>/dev/null || echo "")
    if [ -n "$lambda_check" ]; then
        LAMBDA_FUNCTION_NAME="$lambda_check"
        LAMBDA_REGION="$region"
        print_status "Found Lambda function: $LAMBDA_FUNCTION_NAME in region: $LAMBDA_REGION"
        break
    fi
done

if [ -z "$LAMBDA_FUNCTION_NAME" ]; then
    print_error "No EDA Easy Lambda function found"
    exit 1
fi

# Use the region where Lambda was found
REGION="$LAMBDA_REGION"

# Summary of detected resources
echo ""
print_info "=== DETECTED RESOURCES ==="
print_info "S3 Bucket: $BUCKET_NAME"
print_info "API Gateway: $API_ID"
print_info "Lambda Function: $LAMBDA_FUNCTION_NAME"
print_info "Region: $REGION"
echo ""

# Fix Lambda permissions
echo -e "${BLUE}🔐 Fixing Lambda permissions...${NC}"

# Remove existing permission if it exists (ignore errors)
aws lambda remove-permission \
    --function-name $LAMBDA_FUNCTION_NAME \
    --statement-id api-gateway-invoke \
    --region $REGION 2>/dev/null || true

# Add the correct permission
aws lambda add-permission \
    --function-name $LAMBDA_FUNCTION_NAME \
    --statement-id api-gateway-invoke \
    --action lambda:InvokeFunction \
    --principal apigateway.amazonaws.com \
    --source-arn "arn:aws:execute-api:$REGION:$(aws sts get-caller-identity --query Account --output text):$API_ID/*/*" \
    --region $REGION

print_status "Lambda permission fixed"

# Deploy the API
echo -e "${BLUE}🚀 Deploying API Gateway...${NC}"
aws apigateway create-deployment \
    --rest-api-id $API_ID \
    --stage-name prod \
    --region $REGION

print_status "API Gateway deployed"

# Generate URLs
API_URL="https://$API_ID.execute-api.$REGION.amazonaws.com/prod"
WEBSITE_URL="http://$BUCKET_NAME.s3-website-$REGION.amazonaws.com"

# Update frontend with correct API URL
echo -e "${BLUE}🔧 Updating frontend configuration...${NC}"

# Download current trial.js
aws s3 cp s3://$BUCKET_NAME/js/trial.js trial.js.temp 2>/dev/null || {
    print_error "Could not download trial.js from S3"
    exit 1
}

# Update API URL in the file
sed -i.bak "s|const API_BASE_URL = 'http://127.0.0.1:5002';|const API_BASE_URL = '$API_URL';|g" trial.js.temp
sed -i.bak "s|const API_BASE_URL = 'https://.*\.execute-api\..*\.amazonaws\.com/prod';|const API_BASE_URL = '$API_URL';|g" trial.js.temp

# Upload updated file
aws s3 cp trial.js.temp s3://$BUCKET_NAME/js/trial.js

# Cleanup
rm -f trial.js.temp trial.js.temp.bak

print_status "Frontend updated with correct API URL"

# Test the deployment
echo -e "${BLUE}🧪 Testing deployment...${NC}"

# Test API health
print_info "Testing API health endpoint..."
sleep 5  # Wait for deployment to propagate

response=$(curl -s --max-time 10 "$API_URL/api/health" || echo "failed")

if echo "$response" | grep -q "healthy"; then
    print_status "✅ API is working! Response: $response"
else
    print_error "❌ API test failed. Response: $response"
    print_info "This might be temporary - try testing again in a few minutes"
fi

# Test website
print_info "Testing website..."
website_response=$(curl -s --max-time 10 -o /dev/null -w "%{http_code}" "$WEBSITE_URL" || echo "000")

if [ "$website_response" = "200" ]; then
    print_status "✅ Website is accessible"
else
    print_error "❌ Website test failed. HTTP code: $website_response"
fi

echo ""
echo -e "${GREEN}🎉 Automatic fix completed!${NC}"
echo ""
echo -e "${BLUE}📋 Your EDA Easy Website:${NC}"
echo -e "🌐 Website URL: ${GREEN}$WEBSITE_URL${NC}"
echo -e "🔗 API URL: ${GREEN}$API_URL${NC}"
echo ""
echo -e "${YELLOW}📝 Next Steps:${NC}"
echo "1. Visit your website URL to test the interface"
echo "2. Try the de-embedding trial with demo files"
echo "3. Upload your own S-parameter files"
echo ""
echo -e "${BLUE}🔧 If you need to run tests:${NC}"
echo "./test-deployment.sh \"$WEBSITE_URL\" \"$API_URL\""
