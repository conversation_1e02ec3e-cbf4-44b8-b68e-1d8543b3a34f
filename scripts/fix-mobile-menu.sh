#!/bin/bash

# 🔧 Fix Mobile Menu in All HTML Files

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 Fixing mobile menu in all HTML files...${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

# List of HTML files to fix
html_files=("about.html" "services.html" "pricing.html" "contact.html" "de-embedding-trial.html")

# Fix each HTML file
for file in "${html_files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${BLUE}🔧 Fixing $file...${NC}"
        
        # Create backup
        cp "$file" "${file}.backup"
        
        # Fix the nested nav elements and wrong button class
        sed -i '
            # Remove the duplicate nav line
            /^[[:space:]]*<nav class="menu__body">$/{
                N
                s/<nav class="menu__body">\n[[:space:]]*<nav class="menu__body">/<nav class="menu__body">/
            }
            
            # Fix the button class
            s/class="menu__icon icon-menu"/class="icon-menu"/g
            
            # Remove extra closing nav tag
            /^[[:space:]]*<\/nav>$/{
                N
                s/<\/nav>\n[[:space:]]*<\/div>/<\/nav>\n          <\/div>/
            }
        ' "$file"
        
        print_status "Fixed $file"
    else
        echo -e "${YELLOW}⚠️ $file not found, skipping...${NC}"
    fi
done

# Deploy the fixes to AWS
echo -e "${BLUE}🚀 Deploying mobile menu fixes to AWS...${NC}"
./sync-to-aws.sh

print_status "Mobile menu fixes deployed to AWS"

echo ""
echo -e "${GREEN}🎉 Mobile menu fixed in all HTML files!${NC}"
echo ""
echo -e "${BLUE}📋 What was fixed:${NC}"
echo "✅ Removed duplicate nested <nav class=\"menu__body\"> elements"
echo "✅ Fixed hamburger button class from 'menu__icon icon-menu' to 'icon-menu'"
echo "✅ Fixed menu structure for proper mobile navigation"
echo "✅ Deployed fixes to AWS S3"
echo ""
echo -e "${YELLOW}🧪 Test on mobile now:${NC}"
echo "The hamburger menu should now work properly on mobile devices!"
