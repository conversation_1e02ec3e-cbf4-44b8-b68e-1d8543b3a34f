#!/bin/bash

echo "🎨 Setting up EDA Image Generator with DALL-E"
echo "=============================================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

# Install required packages
echo "📦 Installing required Python packages..."
pip3 install -r requirements.txt

# Check if OpenAI API key is set
if [ -z "$OPENAI_API_KEY" ]; then
    echo ""
    echo "⚠️  OpenAI API key not found in environment variables."
    echo ""
    echo "To set up your API key:"
    echo "1. Get your API key from: https://platform.openai.com/api-keys"
    echo "2. Run: export OPENAI_API_KEY='your-api-key-here'"
    echo "3. Or add it to your ~/.bashrc or ~/.zshrc file"
    echo ""
    echo "Alternative: Edit dalle_image_generator.py and add your key directly"
    echo ""
else
    echo "✅ OpenAI API key found in environment"
fi

echo ""
echo "🚀 Setup complete! You can now run:"
echo "   python3 dalle_image_generator.py"
echo ""
echo "💡 Tips:"
echo "   - The script will generate all 20 images automatically"
echo "   - Images are saved to Website-Images2/ with correct folder structure"
echo "   - Failed generations can be retried by running the script again"
echo "   - There's a 3-second delay between API calls to respect rate limits"
echo ""
