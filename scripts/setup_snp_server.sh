#!/bin/bash

echo "🎯 Setting up SNP Processing Server with scikit-rf"
echo "=================================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed. Please install Python3 first."
    exit 1
fi

echo "✅ Python3 found: $(python3 --version)"

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 first."
    exit 1
fi

echo "✅ pip3 found"

# Install required packages
echo ""
echo "📦 Installing Python dependencies..."
pip3 install -r snp_requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Check if SNP files exist
echo ""
echo "📁 Checking for SNP demo files..."
if [ -f "snpfiles/SfFilterSb.s2p" ]; then
    echo "✅ DUT file found: snpfiles/SfFilterSb.s2p"
else
    echo "❌ DUT file not found: snpfiles/SfFilterSb.s2p"
fi

if [ -f "snpfiles/SfSb.s2p" ]; then
    echo "✅ Fixture file found: snpfiles/SfSb.s2p"
else
    echo "❌ Fixture file not found: snpfiles/SfSb.s2p"
fi

echo ""
echo "🚀 Starting SNP processing server..."
echo "Server will be available at: http://localhost:5000"
echo "Demo page will be available at: skrf_s21_demo.html"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the server
python3 snp_server.py
