#!/bin/bash

echo "🎯 Starting Web S21 Backend (rf_process7.py approach)"
echo "====================================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed. Please install Python3 first."
    exit 1
fi

echo "✅ Python3 found: $(python3 --version)"

# Install required packages if not already installed
echo ""
echo "📦 Installing required Python packages..."
pip3 install flask flask-cors numpy matplotlib

# Check if SNP files exist
echo ""
echo "📁 Checking for SNP demo files..."
if [ -f "snpfiles/SfFilterSb.s2p" ]; then
    echo "✅ DUT file found: snpfiles/SfFilterSb.s2p"
else
    echo "❌ DUT file not found: snpfiles/SfFilterSb.s2p"
fi

if [ -f "snpfiles/SfSb.s2p" ]; then
    echo "✅ Fixture file found: snpfiles/SfSb.s2p"
else
    echo "❌ Fixture file not found: snpfiles/SfSb.s2p"
fi

# Check if ntwk_1 module exists
echo ""
echo "🔧 Checking for ntwk_1 module..."
if [ -f "ntwk_1.py" ]; then
    echo "✅ ntwk_1.py found - will use rf_process7.py approach"
else
    echo "⚠️  ntwk_1.py not found - will use manual parsing fallback"
fi

echo ""
echo "🚀 Starting web backend server..."
echo "Backend will be available at: http://localhost:5001"
echo "API endpoints:"
echo "  - http://localhost:5001/api/demo-files"
echo "  - http://localhost:5001/api/upload-snp"
echo "  - http://localhost:5001/api/health"
echo ""
echo "Open your de-embedding-trial.html page and click 'Load Demo Files'"
echo "Press Ctrl+C to stop the server"
echo ""

# Start the backend server
python3 web_s21_backend.py
