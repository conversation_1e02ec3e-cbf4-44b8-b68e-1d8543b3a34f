#!/bin/bash

# 🧪 Quick Website Test Script

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🧪 Testing EDA Easy Website...${NC}"

# URLs
WEBSITE_URL="http://eda-easy-website-**********.s3-website-us-west-2.amazonaws.com"
API_URL="https://fh73i40lo2.execute-api.us-west-2.amazonaws.com/prod"

# Function to print test results
print_test() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

echo -e "${BLUE}Testing API endpoints...${NC}"

# Test health endpoint
response=$(curl -s --max-time 10 "$API_URL/api/health" | grep -q "healthy" && echo "pass" || echo "fail")
if [ "$response" = "pass" ]; then
    print_test 0 "API Health Check"
else
    print_test 1 "API Health Check"
fi

# Test demo files endpoint
response=$(curl -s --max-time 10 "$API_URL/api/demo-files" | grep -q "dut" && echo "pass" || echo "fail")
if [ "$response" = "pass" ]; then
    print_test 0 "Demo Files Endpoint"
else
    print_test 1 "Demo Files Endpoint"
fi

# Test website
echo -e "${BLUE}Testing website...${NC}"
response=$(curl -s --max-time 10 -o /dev/null -w "%{http_code}" "$WEBSITE_URL")
if [ "$response" = "200" ]; then
    print_test 0 "Website Accessibility"
else
    print_test 1 "Website Accessibility (HTTP $response)"
fi

echo ""
echo -e "${GREEN}🎉 Your EDA Easy website is ready!${NC}"
echo ""
echo -e "${BLUE}📋 URLs:${NC}"
echo -e "🌐 Website: ${GREEN}$WEBSITE_URL${NC}"
echo -e "🔗 API: ${GREEN}$API_URL${NC}"
echo ""
echo -e "${YELLOW}🎯 Next Steps:${NC}"
echo "1. Visit your website URL"
echo "2. Go to the de-embedding trial page"
echo "3. Click 'Load Demo Files' - should work now!"
echo "4. Try uploading S-parameter files"
echo ""
echo -e "${BLUE}🔧 For updates, just run:${NC}"
echo "./sync-to-aws.sh"
