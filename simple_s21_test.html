<!DOCTYPE html>
<html>
<head>
    <title>Simple S21 Test</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .plot-container { width: 100%; height: 500px; margin: 20px 0; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .status { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Simple S21 Plotting Test</h1>
    <button onclick="loadAndPlotS21()">Load SNP Files and Plot S21</button>
    <div id="status" class="status">Ready to load files...</div>
    <div id="plot" class="plot-container"></div>

    <script>
        // Simple SNP parser for S21 extraction
        function parseS2P(content) {
            console.log('Parsing S2P content...');
            const lines = content.split('\n').map(line => line.trim());

            let unit = 'Hz';
            let format = 'MA';
            let impedance = 50;

            const frequencies = [];
            const s21_mag = [];
            const s21_phase = [];

            for (let line of lines) {
                if (line.startsWith('!') || line.length === 0) continue;

                if (line.startsWith('#')) {
                    // Parse header: # Hz S MA R 50
                    const parts = line.substring(1).trim().split(/\s+/);
                    if (parts.length >= 4) {
                        unit = parts[0];
                        format = parts[2];
                        impedance = parseFloat(parts[4]) || 50;
                    }
                    console.log(`Header: ${unit} ${format} R${impedance}`);
                    continue;
                }

                // Parse data line
                const values = line.split(/\s+/).filter(val => val.length > 0).map(parseFloat);
                if (values.length >= 9) {
                    const freq = values[0];
                    const s21_magnitude = values[5]; // S21 magnitude
                    const s21_ph = values[6]; // S21 phase

                    frequencies.push(freq);
                    s21_mag.push(s21_magnitude);
                    s21_phase.push(s21_ph);
                }
            }

            console.log(`Parsed ${frequencies.length} frequency points`);
            console.log(`Sample data: freq=${frequencies[0]}, S21_mag=${s21_mag[0]}, S21_phase=${s21_phase[0]}`);

            return {
                frequencies,
                s21_magnitude: s21_mag,
                s21_phase: s21_phase,
                unit,
                format
            };
        }

        function convertToS21_dB(magnitude) {
            return magnitude.map(mag => 20 * Math.log10(Math.abs(mag)));
        }

        function getFrequencyUnit(frequencies, originalUnit) {
            // Convert to Hz first
            const unitMultipliers = { 'Hz': 1, 'kHz': 1e3, 'MHz': 1e6, 'GHz': 1e9 };
            const multiplier = unitMultipliers[originalUnit] || 1;
            const maxFreqHz = Math.max(...frequencies) * multiplier;

            if (maxFreqHz >= 1e9) return { factor: 1e9, unit: 'GHz' };
            if (maxFreqHz >= 1e6) return { factor: 1e6, unit: 'MHz' };
            if (maxFreqHz >= 1e3) return { factor: 1e3, unit: 'kHz' };
            return { factor: 1, unit: 'Hz' };
        }

        async function loadAndPlotS21() {
            const status = document.getElementById('status');

            try {
                status.innerHTML = 'Loading SNP files...';
                console.log('Starting file load...');

                // Load both files with better error handling
                let dutResponse, fixtureResponse;

                try {
                    dutResponse = await fetch('snpfiles/SfFilterSb.s2p');
                    console.log('DUT response:', dutResponse.status, dutResponse.statusText);
                } catch (e) {
                    console.error('DUT fetch error:', e);
                    throw new Error(`Failed to fetch DUT file: ${e.message}`);
                }

                try {
                    fixtureResponse = await fetch('snpfiles/SfSb.s2p');
                    console.log('Fixture response:', fixtureResponse.status, fixtureResponse.statusText);
                } catch (e) {
                    console.error('Fixture fetch error:', e);
                    throw new Error(`Failed to fetch fixture file: ${e.message}`);
                }

                if (!dutResponse.ok || !fixtureResponse.ok) {
                    throw new Error(`HTTP Error - DUT: ${dutResponse.status}, Fixture: ${fixtureResponse.status}`);
                }

                const [dutText, fixtureText] = await Promise.all([
                    dutResponse.text(),
                    fixtureResponse.text()
                ]);

                status.innerHTML = 'Parsing S-parameter data...';

                // Parse both files
                const dutData = parseS2P(dutText);
                const fixtureData = parseS2P(fixtureText);

                // Convert to dB
                const dutS21_dB = convertToS21_dB(dutData.s21_magnitude);
                const fixtureS21_dB = convertToS21_dB(fixtureData.s21_magnitude);

                status.innerHTML = 'Creating plot...';

                // Determine frequency unit
                const freqUnit = getFrequencyUnit(dutData.frequencies, dutData.unit);
                const unitMultipliers = { 'Hz': 1, 'kHz': 1e3, 'MHz': 1e6, 'GHz': 1e9 };
                const originalMultiplier = unitMultipliers[dutData.unit] || 1;

                const dutFreqs = dutData.frequencies.map(f => (f * originalMultiplier) / freqUnit.factor);
                const fixtureFreqs = fixtureData.frequencies.map(f => (f * originalMultiplier) / freqUnit.factor);

                // Create plot
                const trace1 = {
                    x: dutFreqs,
                    y: dutS21_dB,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Fixture-DUT-Fixture (SfFilterSb.s2p)',
                    line: { color: '#dc3545', width: 2 }
                };

                const trace2 = {
                    x: fixtureFreqs,
                    y: fixtureS21_dB,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Fixture-Fixture (SfSb.s2p)',
                    line: { color: '#28a745', width: 2 }
                };

                const layout = {
                    title: 'S21 Insertion Loss Comparison',
                    xaxis: {
                        title: `Frequency (${freqUnit.unit})`,
                        type: 'linear'
                    },
                    yaxis: {
                        title: 'S21 (dB)'
                    },
                    legend: {
                        x: 0.02,
                        y: 0.98
                    }
                };

                Plotly.newPlot('plot', [trace1, trace2], layout, {responsive: true});

                status.innerHTML = `✅ Success! Plotted ${dutData.frequencies.length} points from DUT file and ${fixtureData.frequencies.length} points from fixture file.`;

            } catch (error) {
                console.error('Error:', error);
                status.innerHTML = `❌ Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
