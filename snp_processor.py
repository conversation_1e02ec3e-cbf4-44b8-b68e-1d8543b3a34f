#!/usr/bin/env python3
"""
SNP file processor using scikit-rf for proper S-parameter handling
"""

import skrf as rf
import numpy as np
import json
import sys
import os

def process_snp_file(file_path):
    """
    Process SNP file using scikit-rf and extract S21 data
    """
    try:
        print(f"Processing file: {file_path}")

        # Load the network using scikit-rf
        network = rf.Network(file_path)

        print(f"Network loaded successfully")
        print(f"Frequency points: {len(network.f)}")
        print(f"S-parameter shape: {network.s.shape}")
        print(f"Network name: {network.name}")

        # Get S-parameters
        s_params = network.s

        # Extract S21 using correct indexing: s[:,1,0] means S21
        # In scikit-rf: s[freq_index, output_port, input_port]
        # S21 = output port 2 (index 1), input port 1 (index 0)
        s21_complex = s_params[:, 1, 0]  # S21 parameter as complex numbers

        print(f"S21 complex shape: {s21_complex.shape}")
        print(f"Sample S21 complex values: {s21_complex[:3]}")

        # Convert to dB: 20*log10(|S21|)
        s21_magnitude = np.abs(s21_complex)
        s21_db = 20 * np.log10(s21_magnitude)

        print(f"Sample S21 magnitude: {s21_magnitude[:3]}")
        print(f"Sample S21 dB: {s21_db[:3]}")

        # Get frequency data
        frequencies = network.f  # Frequency in Hz

        print(f"Frequency range: {np.min(frequencies):.0f} - {np.max(frequencies):.0f} Hz")

        # Determine appropriate frequency unit based on max frequency
        max_freq = np.max(frequencies)
        if max_freq >= 1e9:
            freq_display = frequencies / 1e9
            freq_unit = 'GHz'
        elif max_freq >= 1e6:
            freq_display = frequencies / 1e6
            freq_unit = 'MHz'
        elif max_freq >= 1e3:
            freq_display = frequencies / 1e3
            freq_unit = 'kHz'
        else:
            freq_display = frequencies
            freq_unit = 'Hz'

        print(f"Display unit: {freq_unit}")
        print(f"Display frequency range: {np.min(freq_display):.3f} - {np.max(freq_display):.3f} {freq_unit}")

        return {
            'success': True,
            'frequencies': freq_display.tolist(),
            'freq_unit': freq_unit,
            's21_db': s21_db.tolist(),
            's21_magnitude': s21_magnitude.tolist(),
            's21_phase_deg': np.angle(s21_complex, deg=True).tolist(),
            'num_points': len(frequencies),
            'freq_range': [float(np.min(freq_display)), float(np.max(freq_display))],
            'filename': os.path.basename(file_path)
        }

    except Exception as e:
        print(f"Error processing file: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'filename': os.path.basename(file_path) if file_path else 'unknown'
        }

def process_both_files():
    """
    Process both SNP files and return combined data for plotting
    """
    # File paths
    dut_file = 'snpfiles/SfFilterSb.s2p'
    fixture_file = 'snpfiles/SfSb.s2p'

    # Process both files
    dut_data = process_snp_file(dut_file)
    fixture_data = process_snp_file(fixture_file)

    # Combine results
    result = {
        'dut_data': dut_data,
        'fixture_data': fixture_data,
        'timestamp': str(np.datetime64('now'))
    }

    return result

def main():
    """
    Main function - can be called from command line or web interface
    """
    if len(sys.argv) > 1:
        # Process specific file
        file_path = sys.argv[1]
        result = process_snp_file(file_path)
    else:
        # Process both demo files
        result = process_both_files()

    # Output as JSON
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
