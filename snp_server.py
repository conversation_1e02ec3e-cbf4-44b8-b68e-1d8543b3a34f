#!/usr/bin/env python3
"""
Flask server for SNP file processing using scikit-rf
"""

from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
import skrf as rf
import numpy as np
import os
import tempfile
import json

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

def process_snp_data(file_path):
    """
    Process SNP file using scikit-rf and extract S21 data
    """
    try:
        print(f"Processing file: {file_path}")

        # Load the network using scikit-rf
        network = rf.Network(file_path)

        print(f"Network loaded: {network.name}")
        print(f"Frequency points: {len(network.f)}")
        print(f"S-parameter shape: {network.s.shape}")

        # Get S-parameters
        s_params = network.s

        # Extract S21 using correct indexing: s[:,1,0] means S21
        # In scikit-rf: s[freq_index, output_port, input_port]
        # S21 = output port 2 (index 1), input port 1 (index 0)
        s21_complex = s_params[:, 1, 0]  # S21 parameter as complex numbers

        print(f"S21 complex shape: {s21_complex.shape}")
        print(f"Sample S21 complex values: {s21_complex[:3]}")

        # Convert to dB: 20*log10(|S21|)
        s21_magnitude = np.abs(s21_complex)
        s21_db = 20 * np.log10(s21_magnitude)

        print(f"Sample S21 dB values: {s21_db[:3]}")

        # Get frequency data
        frequencies = network.f  # Frequency in Hz

        # Determine appropriate frequency unit
        max_freq = np.max(frequencies)
        print(f"Max frequency: {max_freq} Hz")

        if max_freq >= 1e9:
            freq_display = frequencies / 1e9
            freq_unit = 'GHz'
        elif max_freq >= 1e6:
            freq_display = frequencies / 1e6
            freq_unit = 'MHz'
        elif max_freq >= 1e3:
            freq_display = frequencies / 1e3
            freq_unit = 'kHz'
        else:
            freq_display = frequencies
            freq_unit = 'Hz'

        print(f"Display unit: {freq_unit}")
        print(f"Sample display frequencies: {freq_display[:3]}")

        return {
            'success': True,
            'frequencies': freq_display.tolist(),
            'freq_unit': freq_unit,
            's21_db': s21_db.tolist(),
            's21_magnitude': s21_magnitude.tolist(),
            's21_phase_deg': np.angle(s21_complex, deg=True).tolist(),
            'num_points': len(frequencies),
            'freq_range': [float(np.min(freq_display)), float(np.max(freq_display))],
            'filename': os.path.basename(file_path)
        }

    except Exception as e:
        print(f"Error processing file: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'filename': os.path.basename(file_path) if file_path else 'unknown'
        }

@app.route('/api/demo-files', methods=['GET'])
def get_demo_files():
    """
    Process demo SNP files and return S21 data
    """
    try:
        # File paths
        dut_file = 'snpfiles/SfFilterSb.s2p'
        fixture_file = 'snpfiles/SfSb.s2p'

        print("Processing demo files...")

        # Check if files exist
        if not os.path.exists(dut_file):
            return jsonify({'success': False, 'error': f'DUT file not found: {dut_file}'}), 404

        if not os.path.exists(fixture_file):
            return jsonify({'success': False, 'error': f'Fixture file not found: {fixture_file}'}), 404

        # Process both files
        dut_data = process_snp_data(dut_file)
        fixture_data = process_snp_data(fixture_file)

        # Combine results
        result = {
            'success': True,
            'dut_data': dut_data,
            'fixture_data': fixture_data,
            'message': 'Demo files processed successfully'
        }

        return jsonify(result)

    except Exception as e:
        print(f"Error in demo files endpoint: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/upload-snp', methods=['POST'])
def upload_snp():
    """
    Handle SNP file upload and processing
    """
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file provided'}), 400

        file = request.files['file']
        file_type = request.form.get('type', 'unknown')  # 'dut' or 'fixture'

        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        # Save file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.s2p') as tmp_file:
            file.save(tmp_file.name)

            # Process the file
            result = process_snp_data(tmp_file.name)
            result['file_type'] = file_type
            result['original_filename'] = file.filename

            # Clean up
            os.unlink(tmp_file.name)

            return jsonify(result)

    except Exception as e:
        print(f"Error in upload endpoint: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """
    Health check endpoint
    """
    return jsonify({
        'status': 'healthy',
        'skrf_version': rf.__version__,
        'message': 'SNP processing server is running'
    })

@app.route('/')
def index():
    """
    Simple index page
    """
    return """
    <h1>SNP Processing Server</h1>
    <p>Endpoints:</p>
    <ul>
        <li><a href="/api/health">/api/health</a> - Health check</li>
        <li>/api/demo-files - Process demo SNP files</li>
        <li>/api/upload-snp - Upload and process SNP files</li>
    </ul>
    """

if __name__ == '__main__':
    print("Starting SNP processing server...")
    print(f"scikit-rf version: {rf.__version__}")

    # Check if demo files exist
    if os.path.exists('snpfiles/SfFilterSb.s2p'):
        print("✅ DUT demo file found")
    else:
        print("❌ DUT demo file not found")

    if os.path.exists('snpfiles/SfSb.s2p'):
        print("✅ Fixture demo file found")
    else:
        print("❌ Fixture demo file not found")

    app.run(debug=True, host='0.0.0.0', port=5000)
