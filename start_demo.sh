#!/bin/bash

echo "🎯 Starting S21 Demo Server"
echo "=========================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed. Please install Python3 first."
    exit 1
fi

echo "✅ Python3 found: $(python3 --version)"

# Check if SNP files exist
echo ""
echo "📁 Checking for SNP demo files..."
if [ -f "snpfiles/SfFilterSb.s2p" ]; then
    echo "✅ DUT file found: snpfiles/SfFilterSb.s2p"
else
    echo "❌ DUT file not found: snpfiles/SfFilterSb.s2p"
    echo "Please make sure the SNP files are in the snpfiles/ directory"
fi

if [ -f "snpfiles/SfSb.s2p" ]; then
    echo "✅ Fixture file found: snpfiles/SfSb.s2p"
else
    echo "❌ Fixture file not found: snpfiles/SfSb.s2p"
    echo "Please make sure the SNP files are in the snpfiles/ directory"
fi

echo ""
echo "🚀 Starting local web server..."
echo "This will automatically open the S21 demo in your browser"
echo "Press Ctrl+C to stop the server"
echo ""

# Start the simple server
python3 simple_server.py
