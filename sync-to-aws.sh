#!/bin/bash
# Backward compatibility wrapper for sync-to-aws.sh
# This script has been moved to scripts/sync-to-aws.sh

echo "⚠️  NOTICE: sync-to-aws.sh has been moved to scripts/sync-to-aws.sh"
echo "🔄 Redirecting to new location..."
echo ""

# Check if the new script exists
if [ -f "scripts/sync-to-aws.sh" ]; then
    exec ./scripts/sync-to-aws.sh "$@"
else
    echo "❌ Error: scripts/sync-to-aws.sh not found"
    echo "Please run: git pull origin main"
    exit 1
fi
