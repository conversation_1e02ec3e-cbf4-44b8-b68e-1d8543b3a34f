#!/bin/bash

# 🔄 Automatic AWS Sync Script
# Automatically syncs local changes to your AWS S3 website

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Auto-detect S3 bucket
BUCKET_NAME=$(aws s3 ls | grep "eda-easy-website" | awk '{print $3}' | head -1)

if [ -z "$BUCKET_NAME" ]; then
    echo -e "${RED}❌ No EDA Easy S3 bucket found${NC}"
    exit 1
fi

echo -e "${BLUE}🔄 Syncing local changes to AWS S3...${NC}"
echo -e "${BLUE}Bucket: $BUCKET_NAME${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check what files have changed
echo -e "${BLUE}📋 Checking for changes...${NC}"

# Sync all website files to S3
aws s3 sync . s3://$BUCKET_NAME \
    --exclude "*.sh" \
    --exclude "*.py" \
    --exclude "*.md" \
    --exclude ".git/*" \
    --exclude "*.json" \
    --exclude "snpfiles/*" \
    --exclude "__pycache__/*" \
    --exclude "*.pyc" \
    --exclude "lambda-deployment/*" \
    --exclude "*.zip" \
    --exclude "*.temp" \
    --exclude "*.bak" \
    --delete

print_status "Website files synced to S3"

# Get website URL
WEBSITE_URL="http://$BUCKET_NAME.s3-website-us-west-2.amazonaws.com"

echo ""
echo -e "${GREEN}🎉 Sync completed!${NC}"
echo -e "${BLUE}Your website: ${GREEN}$WEBSITE_URL${NC}"
echo ""
echo -e "${YELLOW}💡 Pro tip: Bookmark this command for quick updates:${NC}"
echo -e "${BLUE}./sync-to-aws.sh${NC}"

# Optional: Open website in browser
read -p "Open website in browser? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open "$WEBSITE_URL"
    elif command -v open &> /dev/null; then
        open "$WEBSITE_URL"
    else
        echo -e "${YELLOW}Please open: $WEBSITE_URL${NC}"
    fi
fi
