#!/usr/bin/env python3
"""
Test script for OpenAI API key and DALL-E functionality
"""

import os
import requests
import json

def test_api_key():
    """Test if API key is valid"""
    print("🔑 Testing API Key...")
    print("-" * 30)
    
    api_key = os.getenv('OPENAI_API_KEY')
    
    if not api_key:
        print("❌ No API key found in environment")
        print("Set it with: export OPENAI_API_KEY='your-key-here'")
        return False
    
    print(f"✅ API key found: {api_key[:15]}...")
    
    # Test API connection with models endpoint
    headers = {'Authorization': f'Bearer {api_key}'}
    
    try:
        print("📡 Testing API connection...")
        response = requests.get('https://api.openai.com/v1/models', headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ API connection successful!")
            
            # Check if DALL-E 3 is available
            models = response.json()
            dalle_models = [m['id'] for m in models['data'] if 'dall-e' in m['id']]
            print(f"📋 Available DALL-E models: {dalle_models}")
            
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"📝 Details: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_simple_image_generation():
    """Test with a very simple prompt"""
    print("\n🎨 Testing Simple Image Generation...")
    print("-" * 40)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ No API key available")
        return False
    
    # Very simple test prompt
    test_prompt = "A simple electronic circuit board"
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        "model": "dall-e-3",
        "prompt": test_prompt,
        "n": 1,
        "size": "1024x1024",
        "quality": "standard"
    }
    
    print(f"📝 Test prompt: '{test_prompt}'")
    print("📤 Making test request...")
    
    try:
        response = requests.post(
            'https://api.openai.com/v1/images/generations',
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ Image generation successful!")
            result = response.json()
            image_url = result['data'][0]['url']
            print(f"🖼️  Image URL: {image_url[:50]}...")
            return True
        else:
            print(f"❌ Generation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"📝 Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"📝 Raw error: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_eda_prompt():
    """Test with an EDA-specific prompt"""
    print("\n🔬 Testing EDA-Specific Prompt...")
    print("-" * 35)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ No API key available")
        return False
    
    # EDA test prompt
    test_prompt = "Electronics laboratory with computer workstation"
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        "model": "dall-e-3",
        "prompt": test_prompt,
        "n": 1,
        "size": "1024x1024",
        "quality": "standard"
    }
    
    print(f"📝 EDA prompt: '{test_prompt}'")
    print("📤 Making EDA test request...")
    
    try:
        response = requests.post(
            'https://api.openai.com/v1/images/generations',
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ EDA image generation successful!")
            result = response.json()
            image_url = result['data'][0]['url']
            print(f"🖼️  Image URL: {image_url[:50]}...")
            
            # Optionally download and save test image
            save_test = input("💾 Save test image? (y/n): ").lower().strip()
            if save_test == 'y':
                img_response = requests.get(image_url)
                if img_response.status_code == 200:
                    with open('test_eda_image.jpg', 'wb') as f:
                        f.write(img_response.content)
                    print("✅ Test image saved as 'test_eda_image.jpg'")
                else:
                    print("❌ Failed to download test image")
            
            return True
        else:
            print(f"❌ EDA generation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"📝 Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"📝 Raw error: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def check_account_usage():
    """Check API usage and billing"""
    print("\n💳 Checking Account Usage...")
    print("-" * 30)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ No API key available")
        return False
    
    headers = {'Authorization': f'Bearer {api_key}'}
    
    try:
        # Note: Usage endpoint might require different permissions
        response = requests.get('https://api.openai.com/v1/usage', headers=headers, timeout=10)
        
        if response.status_code == 200:
            usage_data = response.json()
            print("✅ Usage data retrieved")
            print(f"📊 Usage: {json.dumps(usage_data, indent=2)}")
        else:
            print(f"⚠️  Usage check failed: {response.status_code}")
            print("💡 This is normal - usage endpoint has limited access")
            
    except Exception as e:
        print(f"⚠️  Usage check error: {str(e)}")
        print("💡 This is normal - usage endpoint has limited access")

def main():
    """Run all tests"""
    print("🧪 OpenAI API & DALL-E Test Suite")
    print("=" * 50)
    
    # Test 1: API Key
    if not test_api_key():
        print("\n❌ API key test failed. Please check your key and try again.")
        return
    
    # Test 2: Account usage (optional)
    check_account_usage()
    
    # Test 3: Simple image generation
    print("\n" + "=" * 50)
    simple_success = test_simple_image_generation()
    
    if not simple_success:
        print("\n❌ Simple image generation failed.")
        print("💡 This might be due to:")
        print("   - Insufficient credits in your OpenAI account")
        print("   - API rate limits")
        print("   - Account restrictions")
        return
    
    # Test 4: EDA-specific prompt
    print("\n" + "=" * 50)
    eda_success = test_eda_prompt()
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 TEST SUMMARY")
    print("=" * 50)
    print(f"✅ API Key: Working")
    print(f"{'✅' if simple_success else '❌'} Simple Generation: {'Working' if simple_success else 'Failed'}")
    print(f"{'✅' if eda_success else '❌'} EDA Generation: {'Working' if eda_success else 'Failed'}")
    
    if simple_success and eda_success:
        print("\n🎉 All tests passed! Your setup is ready for bulk generation.")
        print("💡 You can now run: python3 retry_failed_images.py")
    else:
        print("\n⚠️  Some tests failed. Please check your OpenAI account:")
        print("   - Verify you have sufficient credits")
        print("   - Check if your account has image generation enabled")
        print("   - Try again in a few minutes (rate limiting)")

if __name__ == "__main__":
    main()
