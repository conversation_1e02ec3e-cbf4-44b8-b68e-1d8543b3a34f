#!/usr/bin/env python3
"""
Script to replace all Website-Images references with Website-Images2
"""

import os
import re
from pathlib import Path

def backup_files(files_to_backup):
    """Create backup copies of files before modification"""
    backup_dir = Path("backup_original_files")
    backup_dir.mkdir(exist_ok=True)
    
    for file_path in files_to_backup:
        if Path(file_path).exists():
            backup_path = backup_dir / Path(file_path).name
            with open(file_path, 'r', encoding='utf-8') as src:
                content = src.read()
            with open(backup_path, 'w', encoding='utf-8') as dst:
                dst.write(content)
            print(f"✅ Backed up: {file_path} → {backup_path}")

def update_html_files():
    """Update image references in HTML files"""
    html_files = ['index.html', 'about.html', 'services.html', 'contact.html']
    
    print("🔄 Updating HTML files...")
    print("-" * 30)
    
    for html_file in html_files:
        if not Path(html_file).exists():
            print(f"⚠️  File not found: {html_file}")
            continue
            
        print(f"📝 Processing: {html_file}")
        
        # Read file content
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count replacements
        original_content = content
        
        # Replace Website-Images with Website-Images2
        content = content.replace('Website-Images/', 'Website-Images2/')
        
        # Count how many replacements were made
        replacements = content.count('Website-Images2/') - original_content.count('Website-Images2/')
        
        # Write updated content
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"   ✅ Updated {replacements} image references")

def update_css_files():
    """Update background image references in CSS files"""
    css_files = ['css/style.css']
    
    print("\n🎨 Updating CSS files...")
    print("-" * 25)
    
    for css_file in css_files:
        if not Path(css_file).exists():
            print(f"⚠️  File not found: {css_file}")
            continue
            
        print(f"📝 Processing: {css_file}")
        
        # Read file content
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count replacements
        original_content = content
        
        # Replace Website-Images with Website-Images2 in CSS
        content = content.replace('../Website-Images/', '../Website-Images2/')
        
        # Count how many replacements were made
        replacements = content.count('../Website-Images2/') - original_content.count('../Website-Images2/')
        
        # Write updated content
        with open(css_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"   ✅ Updated {replacements} background image references")

def verify_images_exist():
    """Verify that all referenced images exist in Website-Images2"""
    print("\n🔍 Verifying image files exist...")
    print("-" * 35)
    
    # List of all images that should exist
    required_images = [
        # Home section
        'Website-Images2/Home/Home-About.jpg',
        'Website-Images2/Home/Home-Service-1.png',
        'Website-Images2/Home/Home-Service-2.png', 
        'Website-Images2/Home/Home-Service-3.png',
        'Website-Images2/Home/Home-Testimonial.jpg',
        'Website-Images2/Home/Home-Hero.jpg',
        'Website-Images2/Home/CTA.jpg',
        
        # About section
        'Website-Images2/About/About-Our-Mission.jpg',
        'Website-Images2/About/CTA.jpg',
        
        # Services section
        'Website-Images2/Services/Service-1.jpg',
        'Website-Images2/Services/Service-2.jpg',
        'Website-Images2/Services/Service-3.jpg',
        'Website-Images2/Services/Services-Hero.jpg',
        'Website-Images2/Services/CTA.jpg',
        
        # Contact section
        'Website-Images2/Contact/Contact-Hero.jpg'
    ]
    
    missing_images = []
    existing_images = []
    
    for image_path in required_images:
        if Path(image_path).exists():
            existing_images.append(image_path)
            print(f"   ✅ {image_path}")
        else:
            missing_images.append(image_path)
            print(f"   ❌ {image_path}")
    
    print(f"\n📊 Summary:")
    print(f"   ✅ Existing: {len(existing_images)}")
    print(f"   ❌ Missing: {len(missing_images)}")
    
    if missing_images:
        print(f"\n⚠️  Missing images:")
        for img in missing_images:
            print(f"      {img}")
        print(f"\n💡 Generate missing images with: python3 retry_failed_images.py")
    
    return len(missing_images) == 0

def optimize_images():
    """Suggest image optimization for better web performance"""
    print("\n🚀 Image Optimization Suggestions...")
    print("-" * 40)
    
    image_dir = Path("Website-Images2")
    if not image_dir.exists():
        print("❌ Website-Images2 directory not found")
        return
    
    large_images = []
    total_size = 0
    
    for img_path in image_dir.rglob("*"):
        if img_path.is_file() and img_path.suffix.lower() in ['.jpg', '.jpeg', '.png']:
            size = img_path.stat().st_size
            total_size += size
            
            # Flag images larger than 500KB
            if size > 500 * 1024:
                large_images.append((str(img_path), size))
    
    print(f"📁 Total images size: {total_size / (1024*1024):.1f} MB")
    
    if large_images:
        print(f"\n⚠️  Large images (>500KB):")
        for img_path, size in large_images:
            print(f"   📸 {img_path}: {size / 1024:.0f} KB")
        print(f"\n💡 Consider compressing large images for better web performance")
    else:
        print("✅ All images are reasonably sized")

def main():
    """Main function to update all image references"""
    print("🖼️  Website Image Updater")
    print("=" * 40)
    print("This script will replace all Website-Images references with Website-Images2")
    
    # Confirm before proceeding
    proceed = input("\nProceed with image reference updates? (y/n): ").lower().strip()
    if proceed != 'y':
        print("👋 Update cancelled!")
        return
    
    # Files to backup
    files_to_backup = ['index.html', 'about.html', 'services.html', 'contact.html', 'css/style.css']
    
    # Create backups
    print("\n💾 Creating backups...")
    backup_files(files_to_backup)
    
    # Update HTML files
    update_html_files()
    
    # Update CSS files  
    update_css_files()
    
    # Verify images exist
    all_images_exist = verify_images_exist()
    
    # Optimization suggestions
    optimize_images()
    
    # Final summary
    print("\n" + "=" * 40)
    print("🏁 UPDATE COMPLETE!")
    print("=" * 40)
    
    if all_images_exist:
        print("✅ All image references updated successfully!")
        print("✅ All required images are available!")
        print("\n🌐 Your website now uses EDA-themed images from Website-Images2")
        print("\n🔧 Next steps:")
        print("   1. Test your website: open index.html in browser")
        print("   2. Check all pages load correctly")
        print("   3. Verify images display properly")
    else:
        print("⚠️  Image references updated, but some images are missing!")
        print("🔧 Next steps:")
        print("   1. Generate missing images: python3 retry_failed_images.py")
        print("   2. Test your website after generating missing images")
    
    print(f"\n📁 Backups saved in: backup_original_files/")
    print("💡 If anything goes wrong, restore from backups")

if __name__ == "__main__":
    main()
