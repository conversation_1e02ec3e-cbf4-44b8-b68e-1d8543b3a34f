#!/usr/bin/env python3
"""
Web backend for S21 plotting using the same approach as rf_process7.py
"""

from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
import numpy as np
import os
import tempfile
import json
import re

# Try to import ntwk_1 (your custom module)
try:
    import ntwk_1
    NTWK_AVAILABLE = True
    print("✅ Using ntwk_1 module (same as rf_process7.py)")
except ImportError:
    NTWK_AVAILABLE = False
    print("❌ ntwk_1 module not found, falling back to manual parsing")

app = Flask(__name__)
CORS(app)

def process_snp_with_ntwk1(file_path):
    """
    Process SNP file using ntwk_1 module (same as rf_process7.py)
    """
    try:
        # Load network using the same method as rf_process7.py (line 121)
        ntwk = ntwk_1.Network(file_path)

        # Extract S21 data using the same approach as rf_process7.py
        # Get frequency in GHz (same as rf_process7.py line 297)
        freq_ghz = ntwk.freq / 1e9  # Convert to GHz

        # Get S21 in dB (same as rf_process7.py line 298)
        # For 2-port: S21 is s_db[:, 1, 0] (output port 2, input port 1)
        s21_db = ntwk.s_db[:, 1, 0]  # S21 in dB

        return {
            'success': True,
            'frequencies': freq_ghz.tolist(),
            'freq_unit': 'GHz',
            's21_db': s21_db.tolist(),
            'num_points': len(freq_ghz),
            'freq_range': [float(np.min(freq_ghz)), float(np.max(freq_ghz))],
            'filename': os.path.basename(file_path),
            'method': 'ntwk_1 (rf_process7.py style)'
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'filename': os.path.basename(file_path),
            'method': 'ntwk_1'
        }

def compile_frequency_data(content):
    """
    EXACT same function as ntwk_1.py lines 126-147
    Process lines to gather all data for each frequency into a single line
    """
    compiled_data = {}
    current_freq = None
    for line in content:
        # line = line.strip()
        if line.startswith('#') or line.startswith('!'):
            continue  # Skip comments and headers
        if current_freq and (line.startswith(' ') or line.startswith('\t')):
            # This line is a continuation of the current frequency
            parts = line.split()
            compiled_data[current_freq].extend(parts)
        elif re.match(r"^[0-9.]+", line):
            # A new frequency data starts here
            parts = line.split()
            current_freq = parts[0]  # The first part is the frequency
            if current_freq not in compiled_data:
                compiled_data[current_freq] = []
            # Extend with the rest of the data on the line except the frequency part
            compiled_data[current_freq].extend(parts[1:])

    # Join all data pieces into single lines per frequency
    return [f"{freq} {' '.join(data)}" for freq, data in compiled_data.items()]

def process_snp_manual(file_path):
    """
    EXACT same parsing method as ntwk_1.py that works
    Uses the same approach as your working rf_process7.py + ntwk_1.py
    """
    try:
        print(f"📁 Parsing {file_path} using ntwk_1.py approach...")

        with open(file_path, 'r') as f:
            content = f.readlines()

        # Parse header using regex (same as ntwk_1.py lines 22-36)
        import re
        header_pattern = re.compile(r"#\s+(\w+)\s+(\w+)\s+(\w+)\s+R\s+(\d+)")
        header = None
        for line in content:
            if line.strip().startswith('#'):
                header = header_pattern.search(line.strip())
                break

        if not header:
            raise ValueError("No valid header found in the Touchstone file.")

        # Extract values from header (same as ntwk_1.py)
        unit = header.group(1).lower()
        param_type = header.group(3).lower()  # 'ma' (magnitude-angle) or 'ri' (real-imaginary)
        impedance = float(header.group(4))

        # Determine frequency scale based on the unit (same as ntwk_1.py line 39)
        scale = {'hz': 1, 'khz': 1e3, 'mhz': 1e6, 'ghz': 1e9}.get(unit, 1)

        print(f"   Header parsed: unit={unit}, param_type={param_type}, impedance={impedance}")
        print(f"   Frequency scale: {scale}")

        # Process lines to gather all data for each frequency (same as ntwk_1.py line 68)
        compiled_lines = compile_frequency_data(content)

        frequencies = []
        s21_complex = []

        # Parse each frequency line (same as ntwk_1.py lines 72-83)
        for i, line in enumerate(compiled_lines):
            parts = line.split()
            freq = float(parts[0]) * scale
            frequencies.append(freq)

            if param_type == 'ri':
                # Real/Imaginary format (same as ntwk_1.py line 80)
                # Format: freq S11_real S11_imag S21_real S21_imag S12_real S12_imag S22_real S22_imag
                s21_real = float(parts[3])   # S21 real part at index 3
                s21_imag = float(parts[4])   # S21 imaginary part at index 4
                s21_complex_val = complex(s21_real, s21_imag)
            elif param_type == 'ma':
                # Magnitude/Angle format (same as ntwk_1.py line 82)
                s21_mag = float(parts[3])   # S21 magnitude at index 3
                s21_phase_deg = float(parts[4])    # S21 phase at index 4
                s21_complex_val = np.abs(s21_mag) * np.exp(1j * np.radians(s21_phase_deg))

            s21_complex.append(s21_complex_val)

        print(f"   Parsed {len(frequencies)} frequency points")
        print(f"   Frequency unit: {unit}")

        # Convert to numpy arrays
        frequencies = np.array(frequencies)
        s21_complex = np.array(s21_complex)

        # Calculate S21 magnitude and phase from complex values (same as ntwk_1.py line 151)
        s21_magnitude = np.abs(s21_complex)
        s21_phase = np.angle(s21_complex, deg=True)

        print(f"   Sample S21 complex: {s21_complex[0]}")
        print(f"   Sample S21 magnitude: {s21_magnitude[0]}")
        print(f"   Frequency range: {frequencies[0]:.3e} - {frequencies[-1]:.3e} {unit}")
        print(f"   S21 magnitude range: {np.min(s21_magnitude):.6f} - {np.max(s21_magnitude):.6f}")

        # Convert frequency to GHz (frequencies are already scaled by ntwk_1.py approach)
        # The scale factor already converted to Hz, so we need to convert Hz to GHz
        freq_ghz = frequencies / 1e9  # Convert Hz to GHz

        print(f"   Frequency conversion: {unit} -> GHz")
        print(f"   Converted range: {np.min(freq_ghz):.3f} - {np.max(freq_ghz):.3f} GHz")

        # Convert S21 magnitude to dB (same as ntwk_1.py line 151: 20 * np.log10(np.abs(...)))
        s21_db = 20 * np.log10(s21_magnitude)

        # Handle NaN and infinite values for JSON serialization
        s21_db_clean = []
        freq_ghz_clean = []

        for i, (freq, s21_val) in enumerate(zip(freq_ghz, s21_db)):
            if np.isfinite(freq) and np.isfinite(s21_val):
                freq_ghz_clean.append(float(freq))
                s21_db_clean.append(float(s21_val))
            else:
                print(f"   Skipping invalid data point {i}: freq={freq}, s21_db={s21_val}")

        print(f"   Cleaned data: {len(s21_db_clean)} valid points out of {len(s21_db)} total")

        return {
            'success': True,
            'frequencies': freq_ghz_clean,
            'freq_unit': 'GHz',
            's21_db': s21_db_clean,
            'num_points': len(freq_ghz_clean),
            'freq_range': [float(np.min(freq_ghz_clean)), float(np.max(freq_ghz_clean))],
            'filename': os.path.basename(file_path),
            'method': 'ntwk_1.py_method'
        }

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'filename': os.path.basename(file_path),
            'method': 'manual'
        }

def process_snp_file(file_path):
    """
    Process SNP file using manual parsing (more reliable for your S2P format)
    """
    # Force manual parsing since ntwk_1 is having issues with the S2P format
    print(f"Processing {file_path} with manual parsing...")
    return process_snp_manual(file_path)

@app.route('/api/demo-files', methods=['GET'])
def get_demo_files():
    """
    Process demo SNP files and return S21 data
    """
    try:
        # File paths
        dut_file = 'snpfiles/SfFilterSb.s2p'
        fixture_file = 'snpfiles/SfSb.s2p'

        print("Processing demo files...")

        # Check if files exist
        if not os.path.exists(dut_file):
            return jsonify({'success': False, 'error': f'DUT file not found: {dut_file}'}), 404

        if not os.path.exists(fixture_file):
            return jsonify({'success': False, 'error': f'Fixture file not found: {fixture_file}'}), 404

        # Process both files
        dut_data = process_snp_file(dut_file)
        fixture_data = process_snp_file(fixture_file)

        # Combine results
        result = {
            'success': True,
            'dut_data': dut_data,
            'fixture_data': fixture_data,
            'message': 'Demo files processed successfully using rf_process7.py approach'
        }

        return jsonify(result)

    except Exception as e:
        print(f"Error in demo files endpoint: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/upload-snp', methods=['POST'])
def upload_snp():
    """
    Handle SNP file upload and processing
    """
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file provided'}), 400

        file = request.files['file']
        file_type = request.form.get('type', 'unknown')  # 'dut' or 'fixture'

        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        # Save file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.s2p') as tmp_file:
            file.save(tmp_file.name)

            # Process the file
            result = process_snp_file(tmp_file.name)
            result['file_type'] = file_type
            result['original_filename'] = file.filename

            # Clean up
            os.unlink(tmp_file.name)

            return jsonify(result)

    except Exception as e:
        print(f"Error in upload endpoint: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """
    Health check endpoint
    """
    return jsonify({
        'status': 'healthy',
        'ntwk_1_available': NTWK_AVAILABLE,
        'message': 'S21 processing server using rf_process7.py approach'
    })

@app.route('/')
def index():
    """
    Simple index page
    """
    return """
    <h1>S21 Processing Server (rf_process7.py style)</h1>
    <p>Endpoints:</p>
    <ul>
        <li><a href="/api/health">/api/health</a> - Health check</li>
        <li>/api/demo-files - Process demo SNP files</li>
        <li>/api/upload-snp - Upload and process SNP files</li>
    </ul>
    """

if __name__ == '__main__':
    print("Starting S21 processing server (rf_process7.py approach)...")

    if NTWK_AVAILABLE:
        print("✅ Using ntwk_1 module for S-parameter processing")
    else:
        print("⚠️  Using manual parsing (ntwk_1 not available)")

    # Check if demo files exist
    if os.path.exists('snpfiles/SfFilterSb.s2p'):
        print("✅ DUT demo file found")
    else:
        print("❌ DUT demo file not found")

    if os.path.exists('snpfiles/SfSb.s2p'):
        print("✅ Fixture demo file found")
    else:
        print("❌ Fixture demo file not found")

    app.run(debug=True, host='0.0.0.0', port=5002)
